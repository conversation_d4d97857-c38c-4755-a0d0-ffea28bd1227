import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Modal, TextInput, TouchableOpacity, ScrollView, Dimensions, Animated, Alert, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { createInquiry } from '../utils/api';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 14 : 16;

const EnquiryModal = ({
  visible,
  onClose,
  truckDetails,
  isHireEnquiry = false,
  selectedRentalPeriod = 'daily',
  rentalRates = {},
  onBookingSubmit,
  checkAvailability,
  checkingAvailability = false
}) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    message: '',
    startDate: '',
    endDate: '',
    totalCost: 0
  });

  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [availabilityStatus, setAvailabilityStatus] = useState(null);
  const [loading, setLoading] = useState(false);

  const fadeAnim = useState(new Animated.Value(0))[0];
  const slideAnim = useState(new Animated.Value(100))[0];

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 100,
          duration: 200,
          useNativeDriver: true,
        })
      ]).start();
    }
  }, [visible]);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState(null);

  const handleInputChange = (field, value) => {
    setFormData({ ...formData, [field]: value });
    // Clear error when user starts typing
    if (submitError) {
      setSubmitError(null);
    }

    // Calculate total cost when dates change for hire enquiries
    if (isHireEnquiry && (field === 'startDate' || field === 'endDate')) {
      calculateTotalCost(
        field === 'startDate' ? value : formData.startDate,
        field === 'endDate' ? value : formData.endDate
      );
    }
  };

  const calculateTotalCost = (startDate, endDate) => {
    if (!startDate || !endDate || !rentalRates[selectedRentalPeriod]) return;

    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    let totalCost = 0;
    const rate = rentalRates[selectedRentalPeriod]?.rate || 0;

    switch (selectedRentalPeriod) {
      case 'daily':
        totalCost = diffDays * rate;
        break;
      case 'weekly':
        totalCost = Math.ceil(diffDays / 7) * rate;
        break;
      case 'monthly':
        totalCost = Math.ceil(diffDays / 30) * rate;
        break;
      default:
        totalCost = diffDays * rate;
    }

    setFormData(prev => ({
      ...prev,
      totalCost
    }));
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      setSubmitError('Name is required');
      return false;
    }
    if (!formData.email.trim()) {
      setSubmitError('Email is required');
      return false;
    }
    if (!formData.phone.trim()) {
      setSubmitError('Phone number is required');
      return false;
    }
    if (!formData.message.trim()) {
      setSubmitError('Message is required');
      return false;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setSubmitError('Please enter a valid email address');
      return false;
    }

    // Additional validation for hire enquiries
    if (isHireEnquiry) {
      if (!formData.startDate) {
        setSubmitError('Start date is required');
        return false;
      }
      if (!formData.endDate) {
        setSubmitError('End date is required');
        return false;
      }
      if (new Date(formData.startDate) >= new Date(formData.endDate)) {
        setSubmitError('End date must be after start date');
        return false;
      }
      if (new Date(formData.startDate) < new Date()) {
        setSubmitError('Start date cannot be in the past');
        return false;
      }
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);
      setSubmitError(null);

      if (isHireEnquiry && onBookingSubmit) {
        // Handle hire booking submission
        const bookingData = {
          customerName: formData.name,
          email: formData.email,
          phone: formData.phone,
          startDate: formData.startDate,
          endDate: formData.endDate,
          totalCost: formData.totalCost,
          message: formData.message
        };

        await onBookingSubmit(bookingData);
      } else {
        // Handle regular enquiry submission
        const inquiryData = {
          vehicle_id: truckDetails.vehicle_id || truckDetails.id,
          dealer_id: truckDetails.dealer_id || 1,
          full_name: formData.name,
          email: formData.email,
          phone: formData.phone,
          message: formData.message
        };

        console.log('Submitting inquiry:', inquiryData);
        const response = await createInquiry(inquiryData);
        console.log('Inquiry response:', response);
      }

      // Reset form
      setFormData({
        name: '',
        email: '',
        phone: '',
        message: '',
        startDate: '',
        endDate: '',
        totalCost: 0
      });

      Alert.alert(
        'Success',
        isHireEnquiry ?
          'Your hire request has been submitted successfully! We will contact you with a quote soon.' :
          'Your inquiry has been submitted successfully!'
      );
      onClose();
    } catch (error) {
      console.error('Error submitting inquiry:', error);
      setSubmitError('Failed to submit inquiry. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <Animated.View style={[styles.modalOverlay, { opacity: fadeAnim }]}>
        <Animated.View style={[styles.modalContent, { transform: [{ translateY: slideAnim }] }]}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {isHireEnquiry ? 'Request Hire Quote' : 'Make an Enquiry'}
            </Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.formContainer}>
            {/* Vehicle Info */}
            {truckDetails && (
              <View style={styles.vehicleInfo}>
                <Text style={styles.vehicleInfoTitle}>Enquiring about:</Text>
                <Text style={styles.vehicleInfoText}>
                  {truckDetails.year} {truckDetails.make} {truckDetails.model}
                </Text>
                {truckDetails.price && (
                  <Text style={styles.vehicleInfoPrice}>
                    R{parseFloat(truckDetails.price).toLocaleString()}
                  </Text>
                )}
              </View>
            )}

            {submitError && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{submitError}</Text>
              </View>
            )}

            <Text style={styles.label}>Name *</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your name"
              placeholderTextColor="#666"
              value={formData.name}
              onChangeText={(text) => handleInputChange('name', text)}
              editable={!isSubmitting}
            />

            <Text style={styles.label}>Email *</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your email"
              placeholderTextColor="#666"
              keyboardType="email-address"
              autoCapitalize="none"
              value={formData.email}
              onChangeText={(text) => handleInputChange('email', text)}
              editable={!isSubmitting}
            />

            <Text style={styles.label}>Phone *</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your phone number"
              placeholderTextColor="#666"
              keyboardType="phone-pad"
              value={formData.phone}
              onChangeText={(text) => handleInputChange('phone', text)}
              editable={!isSubmitting}
            />

            <Text style={styles.label}>Message *</Text>
            <TextInput
              style={[styles.input, styles.messageInput]}
              placeholder={isHireEnquiry ?
                `I'm interested in hiring this ${truckDetails.year} ${truckDetails.make} ${truckDetails.model} on a ${selectedRentalPeriod} basis. Please provide more details about availability and terms.` :
                "Enter your message about this vehicle"
              }
              placeholderTextColor="#666"
              multiline
              numberOfLines={4}
              value={formData.message}
              onChangeText={(text) => handleInputChange('message', text)}
              editable={!isSubmitting}
            />

            {/* Hire-specific fields */}
            {isHireEnquiry && (
              <>
                <Text style={styles.label}>Rental Period *</Text>
                <View style={styles.rentalPeriodContainer}>
                  <Text style={styles.rentalPeriodText}>
                    {selectedRentalPeriod.charAt(0).toUpperCase() + selectedRentalPeriod.slice(1)} Rate:
                    R{rentalRates[selectedRentalPeriod] ? Math.round(rentalRates[selectedRentalPeriod].rate).toLocaleString() : 'N/A'}
                  </Text>
                </View>

                <Text style={styles.label}>Start Date *</Text>
                <TouchableOpacity
                  style={styles.dateInput}
                  onPress={() => setShowStartDatePicker(true)}
                >
                  <Text style={[styles.dateText, !formData.startDate && styles.placeholderText]}>
                    {formData.startDate || 'Select start date'}
                  </Text>
                </TouchableOpacity>

                <Text style={styles.label}>End Date *</Text>
                <TouchableOpacity
                  style={styles.dateInput}
                  onPress={() => setShowEndDatePicker(true)}
                >
                  <Text style={[styles.dateText, !formData.endDate && styles.placeholderText]}>
                    {formData.endDate || 'Select end date'}
                  </Text>
                </TouchableOpacity>

                {formData.totalCost > 0 && (
                  <View style={styles.totalCostContainer}>
                    <Text style={styles.totalCostLabel}>Estimated Total Cost:</Text>
                    <Text style={styles.totalCostValue}>R{Math.round(formData.totalCost).toLocaleString()}</Text>
                  </View>
                )}
              </>
            )}

            <TouchableOpacity
              style={[styles.submitButton, isSubmitting && styles.submitButtonDisabled]}
              onPress={handleSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <View style={styles.submitButtonContent}>
                  <ActivityIndicator size="small" color="#fff" style={styles.loadingIndicator} />
                  <Text style={styles.submitButtonText}>Submitting...</Text>
                </View>
              ) : (
                <Text style={styles.submitButtonText}>
                  {isHireEnquiry ? 'Request Quote' : 'Submit Enquiry'}
                </Text>
              )}
            </TouchableOpacity>
          </ScrollView>
        </Animated.View>
      </Animated.View>

      {/* Date Pickers */}
      {showStartDatePicker && (
        <Modal
          transparent={true}
          animationType="fade"
          visible={showStartDatePicker}
          onRequestClose={() => setShowStartDatePicker(false)}
        >
          <View style={styles.datePickerOverlay}>
            <View style={styles.datePickerContainer}>
              <Text style={styles.datePickerTitle}>Select Start Date</Text>
              <TextInput
                style={styles.datePickerInput}
                placeholder="YYYY-MM-DD"
                value={formData.startDate}
                onChangeText={(text) => handleInputChange('startDate', text)}
              />
              <View style={styles.datePickerButtons}>
                <TouchableOpacity
                  style={styles.datePickerButton}
                  onPress={() => setShowStartDatePicker(false)}
                >
                  <Text style={styles.datePickerButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.datePickerButton, styles.datePickerConfirmButton]}
                  onPress={() => setShowStartDatePicker(false)}
                >
                  <Text style={[styles.datePickerButtonText, styles.datePickerConfirmText]}>OK</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}

      {showEndDatePicker && (
        <Modal
          transparent={true}
          animationType="fade"
          visible={showEndDatePicker}
          onRequestClose={() => setShowEndDatePicker(false)}
        >
          <View style={styles.datePickerOverlay}>
            <View style={styles.datePickerContainer}>
              <Text style={styles.datePickerTitle}>Select End Date</Text>
              <TextInput
                style={styles.datePickerInput}
                placeholder="YYYY-MM-DD"
                value={formData.endDate}
                onChangeText={(text) => handleInputChange('endDate', text)}
              />
              <View style={styles.datePickerButtons}>
                <TouchableOpacity
                  style={styles.datePickerButton}
                  onPress={() => setShowEndDatePicker(false)}
                >
                  <Text style={styles.datePickerButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.datePickerButton, styles.datePickerConfirmButton]}
                  onPress={() => setShowEndDatePicker(false)}
                >
                  <Text style={[styles.datePickerButtonText, styles.datePickerConfirmText]}>OK</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    width: '90%',
    maxHeight: '80%',
    maxWidth: 500,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: BASE_FONT_SIZE * 1.25,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 4,
  },
  formContainer: {
    padding: 16,
  },
  label: {
    fontSize: BASE_FONT_SIZE,
    color: '#333',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    color: '#333',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    marginBottom: 16,
    fontSize: BASE_FONT_SIZE,
  },
  messageInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  submitButton: {
    backgroundColor: '#FF0000',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 16,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: BASE_FONT_SIZE,
    fontWeight: 'bold',
  },
  submitButtonDisabled: {
    backgroundColor: '#ccc',
  },
  submitButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingIndicator: {
    marginRight: 8,
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    borderColor: '#f44336',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  errorText: {
    color: '#d32f2f',
    fontSize: BASE_FONT_SIZE * 0.9,
    textAlign: 'center',
  },
  vehicleInfo: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#FF0000',
  },
  vehicleInfoTitle: {
    fontSize: BASE_FONT_SIZE * 0.9,
    color: '#666',
    marginBottom: 4,
  },
  vehicleInfoText: {
    fontSize: BASE_FONT_SIZE,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  vehicleInfoPrice: {
    fontSize: BASE_FONT_SIZE,
    color: '#FF0000',
    fontWeight: 'bold',
  },
  // Hire-specific styles
  rentalPeriodContainer: {
    backgroundColor: '#FFF8F0',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#FFE0B3',
  },
  rentalPeriodText: {
    fontSize: BASE_FONT_SIZE,
    fontWeight: '600',
    color: '#FF8C00',
    textAlign: 'center',
  },
  dateInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    backgroundColor: '#fff',
  },
  dateText: {
    fontSize: BASE_FONT_SIZE,
    color: '#333',
  },
  placeholderText: {
    color: '#666',
  },
  totalCostContainer: {
    backgroundColor: '#f0f8ff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e0e8f0',
    alignItems: 'center',
  },
  totalCostLabel: {
    fontSize: BASE_FONT_SIZE * 0.875,
    color: '#666',
    marginBottom: 4,
  },
  totalCostValue: {
    fontSize: BASE_FONT_SIZE * 1.25,
    fontWeight: 'bold',
    color: '#FF8C00',
  },
  // Date picker styles
  datePickerOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  datePickerContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    width: SCREEN_WIDTH * 0.8,
    maxWidth: 300,
  },
  datePickerTitle: {
    fontSize: BASE_FONT_SIZE * 1.125,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 16,
  },
  datePickerInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: BASE_FONT_SIZE,
  },
  datePickerButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  datePickerButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    alignItems: 'center',
  },
  datePickerConfirmButton: {
    backgroundColor: '#FF8C00',
    borderColor: '#FF8C00',
  },
  datePickerButtonText: {
    fontSize: BASE_FONT_SIZE,
    color: '#333',
  },
  datePickerConfirmText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default EnquiryModal;