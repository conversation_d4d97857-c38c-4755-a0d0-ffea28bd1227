// Test script to check the production API filter behavior
const axios = require('axios');

const API_BASE_URL = 'https://trucksonsale.co.za/api';

async function testProductionAPI() {
  console.log('Testing Production API Filters...\n');
  
  try {
    // Test 1: Get all vehicles (baseline)
    console.log('=== TEST 1: Get All Vehicles (Baseline) ===');
    const allVehiclesResponse = await axios.get(`${API_BASE_URL}/vehicles`, {
      params: {
        limit: 10
      }
    });
    
    const allVehicles = allVehiclesResponse.data.vehicles || [];
    console.log(`Total vehicles returned: ${allVehicles.length}`);
    
    if (allVehicles.length > 0) {
      console.log('Sample vehicle data:');
      const sample = allVehicles[0];
      console.log({
        id: sample.vehicle_id,
        listing_type: sample.listing_type,
        condition_type: sample.condition_type,
        condition_rating: sample.condition_rating,
        color: sample.color,
        engine_type: sample.engine_type,
        make: sample.make,
        model: sample.model
      });
      
      // Show distribution of listing types
      const listingTypes = {};
      const conditionTypes = {};
      allVehicles.forEach(v => {
        listingTypes[v.listing_type] = (listingTypes[v.listing_type] || 0) + 1;
        conditionTypes[v.condition_type] = (conditionTypes[v.condition_type] || 0) + 1;
      });
      
      console.log('\nListing type distribution:', listingTypes);
      console.log('Condition type distribution:', conditionTypes);
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Test 2: Filter by listing_type = 'hire'
    console.log('=== TEST 2: Filter by listing_type = "hire" ===');
    const hireResponse = await axios.get(`${API_BASE_URL}/vehicles`, {
      params: {
        listing_type: 'hire',
        limit: 10
      }
    });
    
    const hireVehicles = hireResponse.data.vehicles || [];
    console.log(`Vehicles with listing_type='hire': ${hireVehicles.length}`);
    
    if (hireVehicles.length > 0) {
      console.log('Hire vehicles listing types:');
      hireVehicles.forEach((v, i) => {
        console.log(`  ${i+1}. ID: ${v.vehicle_id}, listing_type: ${v.listing_type}, make: ${v.make}, model: ${v.model}`);
      });
      
      // Check if all returned vehicles actually have listing_type = 'hire'
      const nonHireVehicles = hireVehicles.filter(v => v.listing_type !== 'hire');
      if (nonHireVehicles.length > 0) {
        console.log(`❌ ERROR: Found ${nonHireVehicles.length} vehicles that are NOT hire type:`);
        nonHireVehicles.forEach(v => {
          console.log(`  - ID: ${v.vehicle_id}, listing_type: ${v.listing_type}`);
        });
      } else {
        console.log('✅ All returned vehicles have listing_type = "hire"');
      }
    } else {
      console.log('No hire vehicles found');
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Test 3: Filter by condition_type = 'used'
    console.log('=== TEST 3: Filter by condition_type = "used" ===');
    const usedResponse = await axios.get(`${API_BASE_URL}/vehicles`, {
      params: {
        condition_type: 'used',
        limit: 10
      }
    });
    
    const usedVehicles = usedResponse.data.vehicles || [];
    console.log(`Vehicles with condition_type='used': ${usedVehicles.length}`);
    
    if (usedVehicles.length > 0) {
      console.log('Used vehicles condition types:');
      usedVehicles.forEach((v, i) => {
        console.log(`  ${i+1}. ID: ${v.vehicle_id}, condition_type: ${v.condition_type}, make: ${v.make}, model: ${v.model}`);
      });
      
      // Check if all returned vehicles actually have condition_type = 'used'
      const nonUsedVehicles = usedVehicles.filter(v => v.condition_type !== 'used');
      if (nonUsedVehicles.length > 0) {
        console.log(`❌ ERROR: Found ${nonUsedVehicles.length} vehicles that are NOT used condition:`);
        nonUsedVehicles.forEach(v => {
          console.log(`  - ID: ${v.vehicle_id}, condition_type: ${v.condition_type}`);
        });
      } else {
        console.log('✅ All returned vehicles have condition_type = "used"');
      }
    } else {
      console.log('No used vehicles found');
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Test 4: Filter by listing_type = 'sale'
    console.log('=== TEST 4: Filter by listing_type = "sale" ===');
    const saleResponse = await axios.get(`${API_BASE_URL}/vehicles`, {
      params: {
        listing_type: 'sale',
        limit: 10
      }
    });
    
    const saleVehicles = saleResponse.data.vehicles || [];
    console.log(`Vehicles with listing_type='sale': ${saleVehicles.length}`);
    
    if (saleVehicles.length > 0) {
      console.log('Sale vehicles listing types:');
      saleVehicles.slice(0, 5).forEach((v, i) => {
        console.log(`  ${i+1}. ID: ${v.vehicle_id}, listing_type: ${v.listing_type}, make: ${v.make}, model: ${v.model}`);
      });
      
      // Check if all returned vehicles actually have listing_type = 'sale'
      const nonSaleVehicles = saleVehicles.filter(v => v.listing_type !== 'sale');
      if (nonSaleVehicles.length > 0) {
        console.log(`❌ ERROR: Found ${nonSaleVehicles.length} vehicles that are NOT sale type:`);
        nonSaleVehicles.slice(0, 3).forEach(v => {
          console.log(`  - ID: ${v.vehicle_id}, listing_type: ${v.listing_type}`);
        });
      } else {
        console.log('✅ All returned vehicles have listing_type = "sale"');
      }
    } else {
      console.log('No sale vehicles found');
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Test 5: Combined filters
    console.log('=== TEST 5: Combined filters (listing_type=sale AND condition_type=used) ===');
    const combinedResponse = await axios.get(`${API_BASE_URL}/vehicles`, {
      params: {
        listing_type: 'sale',
        condition_type: 'used',
        limit: 5
      }
    });
    
    const combinedVehicles = combinedResponse.data.vehicles || [];
    console.log(`Vehicles with listing_type='sale' AND condition_type='used': ${combinedVehicles.length}`);
    
    if (combinedVehicles.length > 0) {
      console.log('Combined filter results:');
      combinedVehicles.forEach((v, i) => {
        console.log(`  ${i+1}. ID: ${v.vehicle_id}, listing_type: ${v.listing_type}, condition_type: ${v.condition_type}, make: ${v.make}`);
      });
      
      // Validate both conditions
      const invalidVehicles = combinedVehicles.filter(v => v.listing_type !== 'sale' || v.condition_type !== 'used');
      if (invalidVehicles.length > 0) {
        console.log(`❌ ERROR: Found ${invalidVehicles.length} vehicles that don't match both criteria:`);
        invalidVehicles.forEach(v => {
          console.log(`  - ID: ${v.vehicle_id}, listing_type: ${v.listing_type}, condition_type: ${v.condition_type}`);
        });
      } else {
        console.log('✅ All returned vehicles match both criteria');
      }
    } else {
      console.log('No vehicles found matching both criteria');
    }
    
  } catch (error) {
    console.error('Error testing API:', error.response?.data || error.message);
  }
}

// Run the test
testProductionAPI();
