{"name": "trucks-on-sale", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.0.4", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.3.17", "@react-navigation/native": "^7.0.19", "@react-navigation/native-stack": "^7.3.3", "axios": "^1.9.0", "expo": "~52.0.41", "expo-dev-client": "~5.0.20", "expo-linear-gradient": "^14.1.5", "expo-status-bar": "~2.0.1", "react": "18.3.1", "react-icons": "^5.5.0", "react-native": "0.76.7", "react-native-dropdown-picker": "^5.4.6", "react-native-safe-area-context": "^5.5.1", "react-native-screens": "^4.10.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}