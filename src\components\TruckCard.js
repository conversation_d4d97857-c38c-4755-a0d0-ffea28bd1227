import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Share, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import ImageCarousel from './ImageCarousel';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 12 : 14;

const TruckCard = ({ item, onPress, width, style }) => {
 
 

  const handleShare = async () => {
    try {
      const vehicleId = item.vehicle_id || item.id;
      const price = item.price ? `R${item.price.toLocaleString()}` : 'Contact for price';
      const shareUrl = `https://trucksonsale.co.za/vehicle.php?id=${vehicleId}`;

      const result = await Share.share({
        message: `Check out this ${item.year} ${item.make} ${item.model} for ${price} on Trucks On Sale!\n${shareUrl}`,
        url: shareUrl,
        title: `${item.year} ${item.make} ${item.model} - Trucks On Sale`
      });

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          console.log('Shared with activity type:', result.activityType);
        } else {
          console.log('Shared successfully');
        }
      }
    } catch (error) {
      console.error('Error sharing vehicle:', error);
      alert('Unable to share this vehicle. Please try again.');
    }
  };

  return (
    <TouchableOpacity style={[styles.card, { width }, style]} onPress={onPress}>
      <View style={styles.imageWrapper}>
        <ImageCarousel
          images={item.images || []}
          width={width || SCREEN_WIDTH * 0.42}
          height={width && width > 300 ? 180 : 140} // Reduced from 200/160
          showIndicators={item.images?.length > 1}
          showCounter={item.images?.length > 1}
          borderRadius={8}
          style={[styles.imageContainer, { height: width && width > 300 ? 180 : 140 }]} // Reduced from 200/160
        />

        {/* Condition Badge Overlay */}
      {/* <View style={styles.conditionBadgeOverlay}>
        {item.condition_type === 'new' ? (
          <View style={[styles.conditionBadge, styles.newBadge]}>
            <Text style={[styles.conditionBadgeText, styles.newBadgeText]}>NEW</Text>
          </View>
        ) : item.condition_type === 'used' ? (
          <View style={[styles.conditionBadge, styles.usedBadge]}>
            <Text style={[styles.conditionBadgeText, styles.usedBadgeText]}>USED</Text>
          </View>
        ) : item.condition_type === 'refurbished' ? (
          <View style={[styles.conditionBadge, styles.refurbishedBadge]}>
            <Text style={[styles.conditionBadgeText, styles.refurbishedBadgeText]}>REFURBISHED</Text>
          </View>
        ) : null}
      </View> */}
      </View>
  
      <View style={styles.detailsContainer}>
        <View style={styles.header}>
          <Text style={styles.title}>{`${item.year} ${item.make}`}</Text>
          <View style={styles.actionButtons}>
            <TouchableOpacity onPress={onPress} style={styles.viewButton}>
              <Ionicons name="eye-outline" size={20} color="#FF0000" />
            </TouchableOpacity>
            <TouchableOpacity onPress={handleShare} style={styles.shareButton}>
              <Ionicons name="share-social-outline" size={20} color="#666" />
            </TouchableOpacity>
          </View>
        </View>
        <Text style={styles.model}>{item.model}</Text>
        <Text style={styles.price}>ZAR {item.price.toLocaleString()}</Text>
        <View style={styles.badgeContainer}>
          {/* Show hire badge if it's a hire vehicle */}
          {(item.listing_type === 'hire' || item.for_hire === true || item.for_hire === 1) && (
            <View style={[styles.badge, styles.hireBadge]}>
              <Text style={[styles.badgeText, styles.hireText]}>FOR HIRE</Text>
            </View>
          )}
          <View style={styles.badge}>
            <Text style={styles.badgeText}>{item.condition_type || 'N/A'}</Text>
          </View>
          <View style={styles.badge}>
            <Text style={styles.badgeText}>{item.region || 'N/A'}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  imageWrapper: {
    position: 'relative',
    width: '100%',
  },
  imageContainer: {
    width: '100%',
    height: 140, // Reduced from 160
  },
  image: {
    width: '100%',
    height: SCREEN_WIDTH > 768 ? 140 : 150, // Reduced from 160/180
    aspectRatio: 16 / 9,
  },
  conditionBadgeOverlay: {
    position: 'absolute',
    top: 8,
    left: 8,
    zIndex: 10,
  },
  conditionBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
  },
  newBadge: {
    backgroundColor: '#4CAF50',
  },
  usedBadge: {
    backgroundColor: '#2196F3',
  },
  refurbishedBadge: {
    backgroundColor: '#FF9800',
  },
  conditionBadgeText: {
    fontSize: BASE_FONT_SIZE * 0.7,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  newBadgeText: {
    color: '#fff',
  },
  usedBadgeText: {
    color: '#fff',
  },
  refurbishedBadgeText: {
    color: '#fff',
  },
  detailsContainer: {
    padding: 10, // Reduced from 12
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  viewButton: {
    padding: 6,
    borderRadius: 4,
    backgroundColor: '#f8f8f8',
  },
  title: {
    fontSize: BASE_FONT_SIZE * 1.0, // Reduced from 1.125
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  model: {
    fontSize: BASE_FONT_SIZE * 0.9, // Reduced from 1.0
    color: '#666',
    marginTop: 3, // Reduced from 4
  },
  price: {
    fontSize: BASE_FONT_SIZE * 1.3, // Reduced from 1.5
    fontWeight: 'bold',
    color: '#FF0000',
    marginTop: 6, // Reduced from 8
  },
  badgeContainer: {
    flexDirection: 'row',
    marginTop: 8, // Reduced from 12
    gap: 6, // Reduced from 8
  },
  badge: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 6, // Reduced from 8
    paddingVertical: 3, // Reduced from 4
    borderRadius: 4,
  },
  badgeText: {
    fontSize: BASE_FONT_SIZE * 0.75, // Reduced from 0.8
    color: '#666',
  },
  hireBadge: {
    backgroundColor: '#FF8C00',
  },
  hireText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  shareButton: {
    padding: 8,
  },
});

export default TruckCard;