import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  ScrollView,
  Linking,
  StatusBar,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

const { width, height } = Dimensions.get('window');
const SIDEBAR_WIDTH = width * 0.75;

const CustomSidebar = ({ onClose, visible, currentRoute = 'Home' }) => {
  const navigation = useNavigation();
  const [pressedItem, setPressedItem] = useState(null);
  const translateX = React.useRef(new Animated.Value(-SIDEBAR_WIDTH)).current;
  const opacity = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(translateX, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 0.5,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(translateX, {
          toValue: -SIDEBAR_WIDTH,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, translateX, opacity]);

  const handleNavigation = (screenName, params = {}) => {
    onClose();
    navigation.navigate(screenName, params);
  };

  const handleExternalLink = (url) => {
    onClose();
    Linking.openURL(url);
  };

  const isActiveRoute = (routeName) => {
    return currentRoute === routeName;
  };

  const renderMenuItem = ({
    icon,
    title,
    onPress,
    isActive = false,
    badge = null,
    color = '#FF0000'
  }) => (
    <TouchableOpacity
      style={[
        styles.menuItem,
        isActive && styles.activeMenuItem,
        pressedItem === title && styles.pressedMenuItem
      ]}
      onPress={onPress}
      onPressIn={() => setPressedItem(title)}
      onPressOut={() => setPressedItem(null)}
      activeOpacity={0.7}
    >
      <View style={[styles.iconContainer, isActive && styles.activeIconContainer]}>
        <Ionicons
          name={icon}
          size={22}
          color={isActive ? '#fff' : color}
        />
      </View>
      <Text style={[
        styles.menuText,
        isActive && styles.activeMenuText
      ]}>
        {title}
      </Text>
      {badge && (
        <View style={styles.badge}>
          <Text style={styles.badgeText}>{badge}</Text>
        </View>
      )}
      <Ionicons
        name="chevron-forward"
        size={16}
        color={isActive ? '#fff' : '#666'}
        style={styles.chevron}
      />
    </TouchableOpacity>
  );

  const renderSectionHeader = (title) => (
    <View style={styles.sectionHeader}>
      <Text style={styles.sectionHeaderText}>{title}</Text>
    </View>
  );

  return (
    <>
      <Animated.View
        style={[
          styles.backdrop,
          {
            opacity: opacity,
          },
        ]}
      >
        <TouchableOpacity
          style={{ width: '100%', height: '100%' }}
          onPress={onClose}
        />
      </Animated.View>

      <Animated.View
        style={[
          styles.container,
          {
            transform: [{ translateX: translateX }],
          },
        ]}
      >
        {/* Header with User Profile Section */}
        <View style={styles.header}>
          <View style={styles.profileSection}>
            <View style={styles.profileIcon}>
              <Ionicons name="person" size={24} color="#FF0000" />
            </View>
            <View style={styles.profileInfo}>
              <Text style={styles.profileName}>Welcome</Text>
              <Text style={styles.profileSubtext}>Trucks On Sale</Text>
            </View>
          </View>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#000" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Navigation Section */}
          {renderSectionHeader('Navigation')}

          {renderMenuItem({
            icon: 'home',
            title: 'Home',
            onPress: () => handleNavigation('Home'),
            isActive: isActiveRoute('Home')
          })}

          {renderMenuItem({
            icon: 'search',
            title: 'Advanced Search',
            onPress: () => handleNavigation('AdvancedSearch'),
            isActive: isActiveRoute('AdvancedSearch')
          })}

          {renderMenuItem({
            icon: 'car-sport',
            title: 'Featured Listings',
            onPress: () => handleNavigation('FeaturedListings'),
            isActive: isActiveRoute('FeaturedListings')
          })}

          {renderMenuItem({
            icon: 'time',
            title: 'Latest Listings',
            onPress: () => handleNavigation('LatestListings'),
            isActive: isActiveRoute('LatestListings')
          })}

          {renderMenuItem({
            icon: 'business',
            title: 'For Hire',
            onPress: () => handleNavigation('Hire'),
            isActive: isActiveRoute('Hire'),
            color: '#FF8C00'
          })}

          {/* <View style={styles.divider} /> */}

          {/* Services Section */}
          {/* {renderSectionHeader('Services')}

          {renderMenuItem({
            icon: 'people',
            title: 'Meet Our Sales Team',
            onPress: () => handleNavigation('SalesTeam'),
            isActive: isActiveRoute('SalesTeam')
          })}

          {renderMenuItem({
            icon: 'call',
            title: 'Call Sales Team',
            onPress: () => handleExternalLink('tel:+**********')
          })}

          {renderMenuItem({
            icon: 'chatbubble-ellipses',
            title: 'Chat with Sales',
            onPress: () => handleNavigation('ChatWithSales'),
            isActive: isActiveRoute('ChatWithSales'),
            badge: 'New'
          })} */}

          {/* <View style={styles.divider} /> */}

          {/* Account Section */}
          {/* {renderSectionHeader('Account')}

          {renderMenuItem({
            icon: 'heart',
            title: 'Saved Vehicles',
            onPress: () => handleNavigation('SavedVehicles'),
            isActive: isActiveRoute('SavedVehicles')
          })}

          {renderMenuItem({
            icon: 'notifications',
            title: 'Notifications',
            onPress: () => handleNavigation('Notifications'),
            isActive: isActiveRoute('Notifications')
          })}

          {renderMenuItem({
            icon: 'settings',
            title: 'Settings',
            onPress: () => handleNavigation('Settings'),
            isActive: isActiveRoute('Settings')
          })}

          <View style={styles.divider} /> */}

          {/* Support Section */}
          {/* {renderSectionHeader('Support')}

          {renderMenuItem({
            icon: 'help-circle',
            title: 'Help & FAQ',
            onPress: () => handleNavigation('Help'),
            isActive: isActiveRoute('Help')
          })}

          {renderMenuItem({
            icon: 'mail',
            title: 'Contact Us',
            onPress: () => handleExternalLink('mailto:<EMAIL>')
          })}

          {renderMenuItem({
            icon: 'information-circle',
            title: 'About',
            onPress: () => handleNavigation('About'),
            isActive: isActiveRoute('About')
          })} */}

        </ScrollView>

        {/* Enhanced Footer */}
        <View style={styles.footer}>
          <View style={styles.footerContent}>
            <View style={styles.footerBrand}>
              <Ionicons name="car-sport" size={20} color="#FF0000" />
              <Text style={styles.footerBrandText}>Trucks On Sale</Text>
            </View>
            <Text style={styles.footerText}>© {new Date().getFullYear()} All Rights Reserved</Text>
            <Text style={styles.footerVersion}>Version 2.0</Text>
          </View>
        </View>
      </Animated.View>
    </>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    zIndex: 1,
  },
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: SIDEBAR_WIDTH,
    height: '100%',
    backgroundColor: '#ffffff',
    zIndex: 2,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 8,
      height: 0,
    },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    borderTopRightRadius: 20,
    borderBottomRightRadius: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? StatusBar.currentHeight + 20 : 20,
    paddingHorizontal: 20,
    paddingBottom: 20,
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
    borderTopRightRadius: 20,
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  profileIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    color: '#2c3e50',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 2,
  },
  profileSubtext: {
    color: '#7f8c8d',
    fontSize: 14,
    fontWeight: '400',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#e9ecef',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  content: {
    flex: 1,
    paddingTop: 8,
  },
  sectionHeader: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    marginTop: 8,
  },
  sectionHeaderText: {
    color: '#7f8c8d',
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    marginHorizontal: 12,
    marginVertical: 2,
    borderRadius: 12,
    minHeight: 56,
  },
  activeMenuItem: {
    backgroundColor: '#FF0000',
    shadowColor: '#FF0000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  pressedMenuItem: {
    backgroundColor: '#f8f9fa',
    transform: [{ scale: 0.98 }],
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  activeIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  menuText: {
    color: '#2c3e50',
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
    lineHeight: 20,
  },
  activeMenuText: {
    color: '#ffffff',
    fontWeight: '600',
  },
  badge: {
    backgroundColor: '#e74c3c',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  badgeText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: '600',
  },
  chevron: {
    marginLeft: 8,
  },
  divider: {
    height: 1,
    backgroundColor: '#e9ecef',
    marginVertical: 16,
    marginHorizontal: 20,
  },
  footer: {
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
    backgroundColor: '#f8f9fa',
    borderBottomRightRadius: 20,
  },
  footerContent: {
    padding: 20,
    alignItems: 'center',
  },
  footerBrand: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  footerBrandText: {
    color: '#2c3e50',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  footerText: {
    color: '#7f8c8d',
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 4,
  },
  footerVersion: {
    color: '#bdc3c7',
    fontSize: 10,
    textAlign: 'center',
  },
});

export default CustomSidebar;