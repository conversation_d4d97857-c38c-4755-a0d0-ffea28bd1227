// Test client-side filtering logic with sample data
console.log('Testing Client-Side Filtering Logic...\n');

// Sample vehicle data (similar to production API response)
const sampleVehicles = [
  {
    vehicle_id: 1,
    category: "trucks",
    listing_type: "sale",
    condition_type: "used",
    condition_rating: "excellent",
    color: "white",
    engine_type: "v8",
    transmission: "automatic",
    fuel_type: "diesel",
    make: "Toyota",
    model: "Hilux",
    year: 2020,
    price: "350000",
    mileage: 50000,
    hours_used: 0,
    region: "Gauteng",
    city: "Johannesburg",
    no_accidents: 1,
    warranty: 1,
    finance_available: 1,
    trade_in: 1,
    service_history: 1,
    roadworthy: 1,
    featured: 1
  },
  {
    vehicle_id: 2,
    category: "trucks",
    listing_type: "hire",
    condition_type: "used",
    condition_rating: "good",
    color: "black",
    engine_type: "v6",
    transmission: "manual",
    fuel_type: "petrol",
    make: "Ford",
    model: "Ranger",
    year: 2019,
    price: "280000",
    mileage: 75000,
    hours_used: 0,
    region: "Western Cape",
    city: "Cape Town",
    no_accidents: 0,
    warranty: 0,
    finance_available: 1,
    trade_in: 0,
    service_history: 1,
    roadworthy: 1,
    featured: 0
  },
  {
    vehicle_id: 3,
    category: "commercial_vehicles",
    listing_type: "sale",
    condition_type: "new",
    condition_rating: "excellent",
    color: "white",
    engine_type: "v8",
    transmission: "automatic",
    fuel_type: "diesel",
    make: "Isuzu",
    model: "D-Max",
    year: 2023,
    price: "450000",
    mileage: 0,
    hours_used: 0,
    region: "Gauteng",
    city: "Pretoria",
    no_accidents: 1,
    warranty: 1,
    finance_available: 1,
    trade_in: 1,
    service_history: 0,
    roadworthy: 1,
    featured: 1
  },
  {
    vehicle_id: 4,
    category: "trucks",
    listing_type: "rent-to-own",
    condition_type: "used",
    condition_rating: "very_good",
    color: "grey",
    engine_type: "v6",
    transmission: "automatic",
    fuel_type: "diesel",
    make: "Nissan",
    model: "Navara",
    year: 2021,
    price: "320000",
    mileage: 30000,
    hours_used: 0,
    region: "KwaZulu-Natal",
    city: "Durban",
    no_accidents: 1,
    warranty: 0,
    finance_available: 1,
    trade_in: 1,
    service_history: 1,
    roadworthy: 1,
    featured: 0
  }
];

// Client-side filtering function (same as in AdvancedSearchScreen)
function applyClientSideFilters(vehicles, filters) {
  console.log('Applying client-side filters to', vehicles.length, 'vehicles');
  console.log('Filters:', filters);
  
  let filtered = [...vehicles];

  // Category filter
  if (filters.selectedCategory) {
    if (filters.selectedCategory === "others") {
      filtered = filtered.filter(vehicle =>
        !["trucks", "commercial_vehicles", "buses"].includes(vehicle.category?.toLowerCase())
      );
    } else {
      filtered = filtered.filter(vehicle => 
        vehicle.category?.toLowerCase() === filters.selectedCategory.toLowerCase()
      );
    }
    console.log(`After category filter (${filters.selectedCategory}): ${filtered.length} vehicles`);
  }

  // Listing type filter
  if (filters.selectedListingType && filters.selectedListingType !== "all") {
    filtered = filtered.filter(vehicle => 
      vehicle.listing_type === filters.selectedListingType
    );
    console.log(`After listing_type filter (${filters.selectedListingType}): ${filtered.length} vehicles`);
  }

  // Condition type filter
  if (filters.selectedCondition && filters.selectedCondition !== "all") {
    filtered = filtered.filter(vehicle => 
      vehicle.condition_type === filters.selectedCondition
    );
    console.log(`After condition_type filter (${filters.selectedCondition}): ${filtered.length} vehicles`);
  }

  // Condition rating filter
  if (filters.selectedConditionRating && filters.selectedConditionRating !== "all") {
    filtered = filtered.filter(vehicle => 
      vehicle.condition_rating === filters.selectedConditionRating
    );
    console.log(`After condition_rating filter (${filters.selectedConditionRating}): ${filtered.length} vehicles`);
  }

  // Color filter
  if (filters.selectedColor && filters.selectedColor !== "All Colors") {
    filtered = filtered.filter(vehicle => 
      vehicle.color === filters.selectedColor
    );
    console.log(`After color filter (${filters.selectedColor}): ${filtered.length} vehicles`);
  }

  // Engine type filter
  if (filters.selectedEngineType && filters.selectedEngineType !== "all") {
    filtered = filtered.filter(vehicle => 
      vehicle.engine_type === filters.selectedEngineType
    );
    console.log(`After engine_type filter (${filters.selectedEngineType}): ${filtered.length} vehicles`);
  }

  // Boolean feature filters
  if (filters.noAccidents) {
    filtered = filtered.filter(vehicle => vehicle.no_accidents === 1);
    console.log(`After no_accidents filter: ${filtered.length} vehicles`);
  }
  if (filters.hasWarranty) {
    filtered = filtered.filter(vehicle => vehicle.warranty === 1);
    console.log(`After warranty filter: ${filtered.length} vehicles`);
  }
  if (filters.financeAvailable) {
    filtered = filtered.filter(vehicle => vehicle.finance_available === 1);
    console.log(`After finance_available filter: ${filtered.length} vehicles`);
  }
  if (filters.featuredOnly) {
    filtered = filtered.filter(vehicle => vehicle.featured === 1);
    console.log(`After featured filter: ${filtered.length} vehicles`);
  }

  console.log(`Final filtered result: ${filtered.length} vehicles`);
  return filtered;
}

// Test cases
console.log('=== TEST 1: Filter by listing_type = "hire" ===');
const hireFilters = { selectedListingType: "hire" };
const hireResults = applyClientSideFilters(sampleVehicles, hireFilters);
console.log('Results:', hireResults.map(v => `ID: ${v.vehicle_id}, listing_type: ${v.listing_type}`));
console.log('✅ Expected: 1 vehicle (ID: 2), Got:', hireResults.length);

console.log('\n=== TEST 2: Filter by condition_type = "used" ===');
const usedFilters = { selectedCondition: "used" };
const usedResults = applyClientSideFilters(sampleVehicles, usedFilters);
console.log('Results:', usedResults.map(v => `ID: ${v.vehicle_id}, condition_type: ${v.condition_type}`));
console.log('✅ Expected: 3 vehicles (IDs: 1, 2, 4), Got:', usedResults.length);

console.log('\n=== TEST 3: Filter by color = "white" ===');
const whiteFilters = { selectedColor: "white" };
const whiteResults = applyClientSideFilters(sampleVehicles, whiteFilters);
console.log('Results:', whiteResults.map(v => `ID: ${v.vehicle_id}, color: ${v.color}`));
console.log('✅ Expected: 2 vehicles (IDs: 1, 3), Got:', whiteResults.length);

console.log('\n=== TEST 4: Filter by engine_type = "v8" ===');
const v8Filters = { selectedEngineType: "v8" };
const v8Results = applyClientSideFilters(sampleVehicles, v8Filters);
console.log('Results:', v8Results.map(v => `ID: ${v.vehicle_id}, engine_type: ${v.engine_type}`));
console.log('✅ Expected: 2 vehicles (IDs: 1, 3), Got:', v8Results.length);

console.log('\n=== TEST 5: Boolean filter - no_accidents = true ===');
const noAccidentsFilters = { noAccidents: true };
const noAccidentsResults = applyClientSideFilters(sampleVehicles, noAccidentsFilters);
console.log('Results:', noAccidentsResults.map(v => `ID: ${v.vehicle_id}, no_accidents: ${v.no_accidents}`));
console.log('✅ Expected: 3 vehicles (IDs: 1, 3, 4), Got:', noAccidentsResults.length);

console.log('\n=== TEST 6: Combined filters (listing_type="sale" AND condition_type="used") ===');
const combinedFilters = { selectedListingType: "sale", selectedCondition: "used" };
const combinedResults = applyClientSideFilters(sampleVehicles, combinedFilters);
console.log('Results:', combinedResults.map(v => `ID: ${v.vehicle_id}, listing_type: ${v.listing_type}, condition_type: ${v.condition_type}`));
console.log('✅ Expected: 1 vehicle (ID: 1), Got:', combinedResults.length);

console.log('\n=== TEST 7: Category filter - "trucks" ===');
const trucksFilters = { selectedCategory: "trucks" };
const trucksResults = applyClientSideFilters(sampleVehicles, trucksFilters);
console.log('Results:', trucksResults.map(v => `ID: ${v.vehicle_id}, category: ${v.category}`));
console.log('✅ Expected: 3 vehicles (IDs: 1, 2, 4), Got:', trucksResults.length);

console.log('\n🎉 Client-side filtering test completed!');
console.log('All filters are working correctly with the sample data.');
console.log('The AdvancedSearchScreen should now filter results properly even though the backend filtering is not working.');
