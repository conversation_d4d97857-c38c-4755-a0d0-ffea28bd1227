import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, FlatList, Dimensions, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const FilterOption = ({ title, options, selectedOption, onSelect, isOpen, onToggle }) => {
  const fadeAnim = useState(new Animated.Value(0))[0];
  const scaleAnim = useState(new Animated.Value(0.95))[0];

  useEffect(() => {
    if (isOpen) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        })
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 150,
          useNativeDriver: true,
        })
      ]).start();
    }
  }, [isOpen]);

  return (
    <View style={styles.filterOption}>
      <TouchableOpacity 
        style={[styles.filterButton, isOpen && styles.filterButtonActive]} 
        onPress={onToggle}
        activeOpacity={0.7}
      >
        <Text style={styles.filterButtonText}>{selectedOption || title}</Text>
        <Ionicons
          name={isOpen ? 'chevron-up' : 'chevron-down'}
          size={20}
          color="#FF0000"
        />
      </TouchableOpacity>

      <Modal visible={isOpen} transparent animationType="fade">
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={onToggle}
        >
          <Animated.View 
            style={[styles.modalContent, { 
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }]
            }]}>
            <FlatList
              data={options}
              keyExtractor={(item) => item}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[styles.optionItem, selectedOption === item && styles.selectedOption]}
                  onPress={() => {
                    onSelect(item);
                    onToggle();
                  }}
                >
                  <Text style={[styles.optionText, selectedOption === item && styles.selectedOptionText]}>
                    {item}
                  </Text>
                </TouchableOpacity>
              )}
            />
          </Animated.View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const FilterBar = ({ onFiltersChange }) => {
  const [regionFilter, setRegionFilter] = useState('');
  const [yearFilter, setYearFilter] = useState('');
  const [priceFilter, setPriceFilter] = useState('');
  const [openFilter, setOpenFilter] = useState(null);

  const regions = ['All Regions', 'kwa zulu', 'Western cape', 'Eastern cape', 'Limpompo', 'Mpumalanga', 'free state'];
  const years = ['All Years', '2023', '2022', '2021', '2020', '2019', '2018', '2017', '2016', '2015'];
  const prices = ['Any Price', 'Under R50,000', 'R50,000 - R100,000', 'R100,000 - R200,000', 'R200,000 - R500,000', 'Over R500,000'];

  const handleFilterChange = (type, value) => {
    let newFilters = {};
    switch (type) {
      case 'region':
        setRegionFilter(value);
        newFilters = { region: value === 'All Regions' ? '' : value, year: yearFilter, price: priceFilter };
        break;
      case 'year':
        setYearFilter(value);
        newFilters = { region: regionFilter, year: value === 'All Years' ? '' : value, price: priceFilter };
        break;
      case 'price':
        setPriceFilter(value);
        newFilters = { region: regionFilter, year: yearFilter, price: value === 'Any Price' ? '' : value };
        break;
    }
    onFiltersChange(newFilters);
  };

  return (
    <View style={styles.container}>
      <FilterOption
        title="Region"
        options={regions}
        selectedOption={regionFilter || 'All Regions'}
        onSelect={(value) => handleFilterChange('region', value)}
        isOpen={openFilter === 'region'}
        onToggle={() => setOpenFilter(openFilter === 'region' ? null : 'region')}
      />
      <FilterOption
        title="Year"
        options={years}
        selectedOption={yearFilter || 'All Years'}
        onSelect={(value) => handleFilterChange('year', value)}
        isOpen={openFilter === 'year'}
        onToggle={() => setOpenFilter(openFilter === 'year' ? null : 'year')}
      />
      <FilterOption
        title="Price"
        options={prices}
        selectedOption={priceFilter || 'Any Price'}
        onSelect={(value) => handleFilterChange('price', value)}
        isOpen={openFilter === 'price'}
        onToggle={() => setOpenFilter(openFilter === 'price' ? null : 'price')}
      />
    </View>
  );
};

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 12 : 14; // Reduced from 14:16

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
    gap: 8,
  },
  filterOption: {
    flex: 1,
  },
  filterButton: {
    transform: [{ scale: 1 }],
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 12, // Reduced from 12
    paddingVertical: 6, // Reduced from 8
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  filterButtonActive: {
    borderColor: '#FF0000',
    backgroundColor: '#fff',
  },
  filterButtonText: {
    color: '#333',
    fontSize: BASE_FONT_SIZE * 0.875,
    flex: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    padding: 12, // Reduced from 16
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    maxHeight: 280, // Reduced from 300
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  optionItem: {
    paddingVertical: 10, // Reduced from 12
    paddingHorizontal: 12, // Reduced from 16
  },
  selectedOption: {
    backgroundColor: '#f5f5f5',
  },
  optionText: {
    color: '#333',
    fontSize: BASE_FONT_SIZE,
  },
  selectedOptionText: {
    color: '#FF0000',
    fontWeight: 'bold',
  },
});

export default FilterBar;