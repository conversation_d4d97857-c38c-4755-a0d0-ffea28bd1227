// Test script to verify the getTotalListingsCount function
console.log('Testing getTotalListingsCount API function...\n');

// Mock the API base URL (you'll need to update this to match your actual backend)
const API_BASE_URL = 'http://localhost:3000/api'; // Update this to your actual backend URL

// Simple test function to check the API
async function testTotalListingsAPI() {
  try {
    console.log('=== Testing Direct API Calls ===');
    
    // Test 1: Direct call to /vehicles endpoint
    console.log('\n1. Testing /vehicles endpoint with limit=1:');
    try {
      const response = await fetch(`${API_BASE_URL}/vehicles?limit=1&page=1`);
      const data = await response.json();
      
      console.log('Response status:', response.status);
      console.log('Response data structure:', Object.keys(data));
      
      if (data.total !== undefined) {
        console.log('✅ Total count found:', data.total);
      } else {
        console.log('❌ No total field in response');
        console.log('Available fields:', Object.keys(data));
        
        if (data.vehicles && Array.isArray(data.vehicles)) {
          console.log('📊 Vehicles array length:', data.vehicles.length);
        }
      }
      
    } catch (error) {
      console.log('❌ /vehicles endpoint failed:', error.message);
    }
    
    // Test 2: Try /vehicles/featured endpoint
    console.log('\n2. Testing /vehicles/featured endpoint:');
    try {
      const response = await fetch(`${API_BASE_URL}/vehicles/featured?limit=1`);
      const data = await response.json();
      
      console.log('Response status:', response.status);
      console.log('Response data structure:', Object.keys(data));
      
      if (data.total !== undefined) {
        console.log('✅ Total count found:', data.total);
      } else {
        console.log('❌ No total field in response');
        if (data.vehicles && Array.isArray(data.vehicles)) {
          console.log('📊 Vehicles array length:', data.vehicles.length);
        }
      }
      
    } catch (error) {
      console.log('❌ /vehicles/featured endpoint failed:', error.message);
    }
    
    // Test 3: Check what a typical vehicle response looks like
    console.log('\n3. Testing sample vehicle data structure:');
    try {
      const response = await fetch(`${API_BASE_URL}/vehicles?limit=3`);
      const data = await response.json();
      
      if (data.vehicles && data.vehicles.length > 0) {
        const sampleVehicle = data.vehicles[0];
        console.log('✅ Sample vehicle fields:', Object.keys(sampleVehicle));
        
        // Check for image data
        if (sampleVehicle.images) {
          console.log('📷 Vehicle has images:', Array.isArray(sampleVehicle.images) ? sampleVehicle.images.length : 'not an array');
        }
        
        // Check for condition data
        if (sampleVehicle.condition_type) {
          console.log('🏷️ Vehicle condition_type:', sampleVehicle.condition_type);
        }
        
        // Check for hire status
        if (sampleVehicle.listing_type || sampleVehicle.for_hire) {
          console.log('🚗 Vehicle listing info:', {
            listing_type: sampleVehicle.listing_type,
            for_hire: sampleVehicle.for_hire
          });
        }
      }
      
    } catch (error) {
      console.log('❌ Sample data test failed:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Instructions for manual testing
console.log('=== Manual Testing Instructions ===');
console.log('');
console.log('To test the getTotalListingsCount function:');
console.log('');
console.log('1. Make sure your backend server is running');
console.log('2. Update the API_BASE_URL in this script to match your backend');
console.log('3. Run this script: node test-total-listings-api.js');
console.log('');
console.log('Expected behavior:');
console.log('✅ Should return a number > 0 if vehicles exist in database');
console.log('✅ Should return 0 if no vehicles exist');
console.log('✅ Should handle errors gracefully');
console.log('');
console.log('Common issues:');
console.log('❌ Backend not running → Connection refused error');
console.log('❌ Wrong API URL → 404 or connection errors');
console.log('❌ Database empty → Returns 0 (this is correct behavior)');
console.log('❌ Backend error → Check backend logs');
console.log('');

// Debugging tips
console.log('=== Debugging Tips ===');
console.log('');
console.log('If you see "0 vehicles" in the AdvancedSearchScreen:');
console.log('');
console.log('1. Check browser/app console for API errors');
console.log('2. Verify backend is running and accessible');
console.log('3. Test API endpoints manually:');
console.log('   - Open browser to: http://your-backend-url/api/vehicles?limit=1');
console.log('   - Should return JSON with vehicles array and/or total field');
console.log('');
console.log('4. Check backend logs for errors');
console.log('5. Verify database has vehicle records');
console.log('');
console.log('6. Test the React Native app API calls:');
console.log('   - Look for console.log messages starting with "API:"');
console.log('   - Check for error messages in the app logs');
console.log('');

// React Native specific debugging
console.log('=== React Native App Debugging ===');
console.log('');
console.log('In the AdvancedSearchScreen, you should see console logs like:');
console.log('✅ "API: Fetching total listings count"');
console.log('✅ "API: getAllVehicles response: {...}"');
console.log('✅ "API: Got total count from /vehicles: X"');
console.log('');
console.log('If you see error logs:');
console.log('❌ Check the error message for clues');
console.log('❌ Verify the API base URL in your app configuration');
console.log('❌ Check network connectivity');
console.log('❌ Verify backend CORS settings allow your app domain');
console.log('');

console.log('=== Quick Fix Suggestions ===');
console.log('');
console.log('If the count is still 0 after fixing API issues:');
console.log('');
console.log('1. Temporary hardcoded value for testing:');
console.log('   - In AdvancedSearchScreen.js, replace the useEffect with:');
console.log('   - setTotalListings(1234); // Hardcoded for testing');
console.log('');
console.log('2. Use existing vehicle data:');
console.log('   - Count vehicles from search results');
console.log('   - Use the length of any existing vehicle arrays');
console.log('');
console.log('3. Check if your backend returns total in a different field:');
console.log('   - Look for: count, totalCount, totalRecords, etc.');
console.log('   - Update the API function accordingly');
console.log('');

// Uncomment the line below to run the actual test (requires updating API_BASE_URL)
// testTotalListingsAPI();

console.log('🔧 Update the API_BASE_URL variable above and uncomment the last line to run the actual test.');
