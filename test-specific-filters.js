// Detailed test of specific filters on production API
const axios = require('axios');

const API_BASE_URL = 'https://trucksonsale.co.za/api';

async function testSpecificFilters() {
  console.log('🔍 Testing Specific Filters on Production API...\n');
  
  try {
    // Test 1: Baseline - no filters
    console.log('=== TEST 1: Baseline (No Filters) ===');
    const baselineResponse = await axios.get(`${API_BASE_URL}/vehicles`, {
      params: { limit: 5 }
    });
    
    const baselineVehicles = baselineResponse.data.vehicles || [];
    console.log(`Baseline vehicles returned: ${baselineVehicles.length}`);
    
    if (baselineVehicles.length > 0) {
      console.log('Sample vehicles:');
      baselineVehicles.forEach((v, i) => {
        console.log(`  ${i+1}. ID: ${v.vehicle_id}, listing_type: ${v.listing_type}, condition_type: ${v.condition_type}`);
      });
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Test 2: Test listing_type=hire specifically
    console.log('=== TEST 2: listing_type=hire ===');
    const hireResponse = await axios.get(`${API_BASE_URL}/vehicles`, {
      params: { 
        listing_type: 'hire',
        limit: 5 
      }
    });
    
    const hireVehicles = hireResponse.data.vehicles || [];
    console.log(`Vehicles with listing_type=hire: ${hireVehicles.length}`);
    console.log('Request URL:', `${API_BASE_URL}/vehicles?listing_type=hire&limit=5`);
    
    if (hireVehicles.length > 0) {
      console.log('Returned vehicles:');
      hireVehicles.forEach((v, i) => {
        const isCorrect = v.listing_type === 'hire' ? '✅' : '❌';
        console.log(`  ${i+1}. ${isCorrect} ID: ${v.vehicle_id}, listing_type: ${v.listing_type}, make: ${v.make}`);
      });
      
      const correctVehicles = hireVehicles.filter(v => v.listing_type === 'hire');
      console.log(`\nResult: ${correctVehicles.length}/${hireVehicles.length} vehicles are correctly filtered`);
      
      if (correctVehicles.length === hireVehicles.length) {
        console.log('🎉 listing_type filter is WORKING!');
      } else {
        console.log('💥 listing_type filter is NOT WORKING!');
      }
    } else {
      console.log('No vehicles returned - this could indicate the filter is working but no hire vehicles exist');
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Test 3: Test condition_type=used specifically
    console.log('=== TEST 3: condition_type=used ===');
    const usedResponse = await axios.get(`${API_BASE_URL}/vehicles`, {
      params: { 
        condition_type: 'used',
        limit: 5 
      }
    });
    
    const usedVehicles = usedResponse.data.vehicles || [];
    console.log(`Vehicles with condition_type=used: ${usedVehicles.length}`);
    console.log('Request URL:', `${API_BASE_URL}/vehicles?condition_type=used&limit=5`);
    
    if (usedVehicles.length > 0) {
      console.log('Returned vehicles:');
      usedVehicles.forEach((v, i) => {
        const isCorrect = v.condition_type === 'used' ? '✅' : '❌';
        console.log(`  ${i+1}. ${isCorrect} ID: ${v.vehicle_id}, condition_type: ${v.condition_type}, make: ${v.make}`);
      });
      
      const correctVehicles = usedVehicles.filter(v => v.condition_type === 'used');
      console.log(`\nResult: ${correctVehicles.length}/${usedVehicles.length} vehicles are correctly filtered`);
      
      if (correctVehicles.length === usedVehicles.length) {
        console.log('🎉 condition_type filter is WORKING!');
      } else {
        console.log('💥 condition_type filter is NOT WORKING!');
      }
    } else {
      console.log('No vehicles returned - this could indicate the filter is working but no used vehicles exist');
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Test 4: Test with invalid filter value
    console.log('=== TEST 4: Invalid Filter Value (listing_type=invalid) ===');
    const invalidResponse = await axios.get(`${API_BASE_URL}/vehicles`, {
      params: { 
        listing_type: 'invalid',
        limit: 3 
      }
    });
    
    const invalidVehicles = invalidResponse.data.vehicles || [];
    console.log(`Vehicles with listing_type=invalid: ${invalidVehicles.length}`);
    
    if (invalidVehicles.length === 0) {
      console.log('✅ Correctly returned 0 vehicles for invalid filter value');
    } else {
      console.log('❌ Returned vehicles for invalid filter value - filter not working');
      invalidVehicles.forEach((v, i) => {
        console.log(`  ${i+1}. ID: ${v.vehicle_id}, listing_type: ${v.listing_type}`);
      });
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Test 5: Test color filter
    console.log('=== TEST 5: color=grey ===');
    const greyResponse = await axios.get(`${API_BASE_URL}/vehicles`, {
      params: { 
        color: 'grey',
        limit: 5 
      }
    });
    
    const greyVehicles = greyResponse.data.vehicles || [];
    console.log(`Vehicles with color=grey: ${greyVehicles.length}`);
    
    if (greyVehicles.length > 0) {
      console.log('Returned vehicles:');
      greyVehicles.forEach((v, i) => {
        const isCorrect = v.color === 'grey' ? '✅' : '❌';
        console.log(`  ${i+1}. ${isCorrect} ID: ${v.vehicle_id}, color: ${v.color}, make: ${v.make}`);
      });
      
      const correctVehicles = greyVehicles.filter(v => v.color === 'grey');
      if (correctVehicles.length === greyVehicles.length) {
        console.log('🎉 color filter is WORKING!');
      } else {
        console.log('💥 color filter is NOT WORKING!');
      }
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Summary
    console.log('=== SUMMARY ===');
    console.log('Based on the test results:');
    
    // Check if any filters are working
    const hireCorrect = hireVehicles.filter(v => v.listing_type === 'hire').length === hireVehicles.length;
    const usedCorrect = usedVehicles.filter(v => v.condition_type === 'used').length === usedVehicles.length;
    const greyCorrect = greyVehicles.filter(v => v.color === 'grey').length === greyVehicles.length;
    
    if (hireCorrect && usedCorrect && greyCorrect) {
      console.log('✅ ALL FILTERS ARE WORKING CORRECTLY!');
    } else {
      console.log('❌ FILTERS ARE NOT WORKING PROPERLY');
      console.log('The production server still needs the Vehicle.js search method fixes deployed.');
      console.log('\nRequired fixes:');
      console.log('1. Add specific filter handlers for listing_type, condition_type, etc.');
      console.log('2. Remove or fix the generic else clause that processes all parameters');
      console.log('3. Add proper validation for "all" values and boolean filters');
    }
    
  } catch (error) {
    console.error('❌ Error testing production API:', error.response?.data || error.message);
  }
}

// Run the test
testSpecificFilters();
