import React, { useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { StatusBar } from 'expo-status-bar';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import HomeScreen from './src/screens/HomeScreen';
import OnSaleScreen from './src/screens/OnSaleScreen';
import Rent2OwnScreen from './src/screens/Rent2OwnScreen';
import HireScreen from './src/screens/HireScreen';
import AuctionScreen from './src/screens/AuctionScreen';
import TruckDetailsScreen from './src/screens/TruckDetailsScreen';
import HireDetailsScreen from './src/screens/HireDetailsScreen';
import SellTruckScreen from './src/screens/SellTruckScreen';
import SalesTeamScreen from './src/screens/SalesTeamScreen';
import CustomSidebar from './src/components/CustomSidebar';
import AdvancedSearchScreen from './src/screens/AdvancedSearchScreen';
import ChatWithSalesScreen from './src/components/ChatWithSales';
import AdvancedSearchDetailsScreen from './src/screens/AdvancedSearchDetailsScreen';
import FeaturedListingsScreen from './src/screens/FeaturedListingsScreen';
import SimpleFeaturedListingsScreen from './src/screens/SimpleFeaturedListingsScreen';
import SimpleLatestListingsScreen from './src/screens/SimpleLatestListingsScreen';
import LatestListingsScreen from './src/screens/LatestListingsScreen';
import ErrorBoundary from './src/components/ErrorBoundary';
import SafeScreenWrapper from './src/components/SafeScreenWrapper';
import ApiDebugger from './src/components/ApiDebugger';

const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

// Bottom Tab Navigator Component
function MainTabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'HomeTab') {
            iconName = focused ? 'car' : 'car-outline';
          } else if (route.name === 'OnSal') {
            iconName = focused ? 'car' : 'car-outline';
          } else if (route.name === 'Rent2Own') {
            iconName = focused ? 'card' : 'card-outline';
          } else if (route.name === 'Hire') {
            iconName = focused ? 'time' : 'time-outline';
          } else if (route.name === 'Auction') {
            iconName = focused ? 'hammer' : 'hammer-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#FF0000',
        tabBarInactiveTintColor: 'gray',
        tabBarStyle: {
          backgroundColor: '#fff',
          borderTopWidth: 1,
          borderTopColor: '#e0e0e0',
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
        },
        headerShown: false,
      })}
    >
      <Tab.Screen
        name="HomeTab"
        component={HomeScreen}
        options={{
          tabBarLabel: 'On Sale',
        }}
      />
      {/* <Tab.Screen
        name="OnSale"
        component={OnSaleScreen}
        options={{
          tabBarLabel: 'On Sale',
        }}
      /> */}
      <Tab.Screen
        name="Rent2Own"
        component={Rent2OwnScreen}
        options={{
          tabBarLabel: 'Rent2Own',
        }}
      />
      <Tab.Screen
        name="Hire"
        component={HireScreen}
        options={{
          tabBarLabel: 'Hire',
        }}
      />
      <Tab.Screen
        name="Auction"
        component={AuctionScreen}
        options={{
          tabBarLabel: 'Auction',
        }}
      />
    </Tab.Navigator>
  );
}

// Component to handle navigation state and sidebar
function AppNavigator({ currentRoute }) {
  const [sidebarVisible, setSidebarVisible] = useState(false);

  const toggleSidebar = () => {
    setSidebarVisible(!sidebarVisible);
  };

  return (
    <View style={{ flex: 1 }}>
      <StatusBar style="light" backgroundColor="#1a1a1a" translucent={false} />
      <Stack.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: '#1a1a1a',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
          headerLeft: () => (
            <TouchableOpacity onPress={toggleSidebar} style={{ marginRight: 15 }}>
              <Ionicons name="menu" size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      >
            <Stack.Screen
              name="Home"
              component={MainTabNavigator}
              options={{
                title: 'Trucks On Sale',
              }}
            />
            <Stack.Screen
              name="HomeScreen"
              component={HomeScreen}
              options={{
                title: 'Trucks On Sale',
              }}
            />
            <Stack.Screen
              name="TruckDetails"
              component={TruckDetailsScreen}
              options={{
                title: 'Truck Details',
              }}
            />
            <Stack.Screen
              name="HireDetails"
              component={HireDetailsScreen}
              options={{
                title: 'Hire Details',
              }}
            />
            <Stack.Screen
              name="SellTruck"
              component={SellTruckScreen}
              options={{
                title: 'Sell Your Truck',
              }}
            />
            <Stack.Screen
              name="SalesTeam"
              component={SalesTeamScreen}
              options={{
                title: 'Our Sales Team',
              }}
            />
            <Stack.Screen
              name="AdvancedSearch"
              component={AdvancedSearchScreen}
              options={{
                title: 'Advanced Search',
              }}
            />
            <Stack.Screen
              name="ChatWithSales"
              component={ChatWithSalesScreen}
              options={{
                title: 'Chat with Sales',
              }}
            />
            <Stack.Screen
              name="AdvancedSearchDetails"
              component={AdvancedSearchDetailsScreen}
              options={{
                title: 'Vehicle Details',
              }}
            />
            <Stack.Screen
              name="FeaturedListings"
              component={FeaturedListingsScreen}
              options={{
                title: 'Featured Listings',
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="LatestListings"
              component={LatestListingsScreen}
              options={{
                title: 'Latest Listings',
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ApiDebugger"
              component={ApiDebugger}
              options={{
                title: 'API Debugger',
                headerShown: true,
              }}
            />
        </Stack.Navigator>

        {sidebarVisible && (
          <CustomSidebar
            onClose={toggleSidebar}
            visible={sidebarVisible}
            currentRoute={currentRoute}
          />
        )}
    </View>
  );
}

export default function App() {
  const [currentRoute, setCurrentRoute] = useState('Home');

  const handleNavigationStateChange = (state) => {
    if (state) {
      const route = state.routes[state.index];
      if (route) {
        setCurrentRoute(route.name);
      }
    }
  };

  return (
    <SafeAreaProvider>
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <NavigationContainer onStateChange={handleNavigationStateChange}>
          <AppNavigator currentRoute={currentRoute} />
        </NavigationContainer>
      </SafeAreaView>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
});