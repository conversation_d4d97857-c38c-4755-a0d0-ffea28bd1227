import axios from 'axios';

// Base URL for API requests - using production server with client-side filtering
const API_BASE_URL = 'https://trucksonsale.co.za/api';      // Production

console.log('API Base URL:', API_BASE_URL);

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 15000, // 15 second timeout
});

// Vehicle endpoints
export const getLatestVehicles = async (limit = 15, random = false, category = 'trucks', options = {}) => {
  try {
    console.log('API: Fetching latest vehicles with params:', { limit, random, category });

    // Map frontend categories to database categories
    const categoryMapping = {
      'trucks': 'trucks',
      'commercial_vehicles': 'commercial_vehicles',
      'buses': 'buses'
    };

    const requestConfig = {
      params: {
        sort: random ? 'random' : 'created_at',
        order: 'desc',
        limit,
        random: random.toString()
      },
      timeout: 10000, // 10 second timeout
      ...options
    };

    // Handle category filtering
    if (category === 'others') {
      // For "others", exclude the main categories
      requestConfig.params.exclude_categories = 'trucks,commercial_vehicles,buses';
      console.log('API: Setting exclude_categories for "others":', requestConfig.params.exclude_categories);
    } else {
      // For specific categories, use the mapped category
      const dbCategory = categoryMapping[category] || category;
      requestConfig.params.category = dbCategory;
      console.log('API: Setting category filter:', dbCategory);
    }

    const response = await api.get('/vehicles', requestConfig);

    // Validate response structure
    if (!response || !response.data) {
      throw new Error('Invalid response structure');
    }

    // Ensure vehicles array exists and process images
    const vehicles = (response.data.vehicles || []).map(vehicle => {
      // Process images to ensure proper URLs
      if (vehicle.images && Array.isArray(vehicle.images)) {
        vehicle.images = vehicle.images
          .sort((a, b) => {
            // Sort by is_primary first, then by image_order
            if (a.is_primary && !b.is_primary) return -1;
            if (!a.is_primary && b.is_primary) return 1;
            return (a.image_order || 0) - (b.image_order || 0);
          })
          .map(image => ({
            ...image,
            // Ensure image_path is a full URL
            image_path: image.image_path.startsWith('http')
              ? image.image_path
              : `${API_BASE_URL.replace('/api', '')}/${image.image_path}`
          }));
      }
      return vehicle;
    });

    // Debug logging for image URLs
    if (vehicles.length > 0) {
      console.log('Latest vehicles response count:', vehicles.length);
      console.log('Categories found:', [...new Set(vehicles.map(v => v.category))]);
      if (vehicles[0].images?.length > 0) {
        console.log('First vehicle image URL:', vehicles[0].images[0].image_path);
        console.log('Total images for first vehicle:', vehicles[0].images.length);
      }
    }

    return {
      vehicles,
      total: response.data.total || vehicles.length,
      ...response.data
    };
  } catch (error) {
    console.error('Error fetching latest vehicles:', error.response?.data || error.message);

    // Return safe fallback data instead of throwing
    return {
      vehicles: [],
      total: 0,
      error: error.message || 'Failed to fetch vehicles'
    };
  }
};

export const getFeaturedVehicles = async (limit = 15, random = false, category = 'trucks', options = {}) => {
  try {
    console.log('API: Fetching featured vehicles with params:', { limit, random, category });

    // Map frontend categories to database categories
    const categoryMapping = {
      'trucks': 'trucks',
      'commercial_vehicles': 'commercial_vehicles',
      'buses': 'buses'
    };

    const requestConfig = {
      params: {
        limit,
        random: random.toString()
      },
      timeout: 10000, // 10 second timeout
      ...options
    };

    // Handle category filtering
    if (category === 'others') {
      // For "others", exclude the main categories
      requestConfig.params.exclude_categories = 'trucks,commercial_vehicles,buses';
      console.log('API: Setting exclude_categories for "others" in getFeaturedVehicles:', requestConfig.params.exclude_categories);
    } else {
      // For specific categories, use the mapped category
      const dbCategory = categoryMapping[category] || category;
      requestConfig.params.category = dbCategory;
      console.log('API: Setting category filter in getFeaturedVehicles:', dbCategory);
    }

    const response = await api.get('/vehicles/featured', requestConfig);

    // Validate response structure
    if (!response || !response.data) {
      throw new Error('Invalid response structure');
    }

    // Ensure vehicles array exists and process images
    const vehicles = (response.data.vehicles || []).map(vehicle => {
      // Process images to ensure proper URLs
      if (vehicle.images && Array.isArray(vehicle.images)) {
        vehicle.images = vehicle.images
          .sort((a, b) => {
            // Sort by is_primary first, then by image_order
            if (a.is_primary && !b.is_primary) return -1;
            if (!a.is_primary && b.is_primary) return 1;
            return (a.image_order || 0) - (b.image_order || 0);
          })
          .map(image => ({
            ...image,
            // Ensure image_path is a full URL
            image_path: image.image_path.startsWith('http')
              ? image.image_path
              : `${API_BASE_URL.replace('/api', '')}/${image.image_path}`
          }));
      }
      return vehicle;
    });

    // Debug logging for image URLs
    if (vehicles.length > 0) {
      console.log('Featured vehicles response count:', vehicles.length);
      console.log('Categories found:', [...new Set(vehicles.map(v => v.category))]);
      if (vehicles[0].images?.length > 0) {
        console.log('First featured vehicle image URL:', vehicles[0].images[0].image_path);
        console.log('Total images for first vehicle:', vehicles[0].images.length);
      }
    }

    return {
      vehicles,
      total: response.data.total || vehicles.length,
      ...response.data
    };
  } catch (error) {
    console.error('Error fetching featured vehicles:', error.response?.data || error.message);

    // Return safe fallback data instead of throwing
    return {
      vehicles: [],
      total: 0,
      error: error.message || 'Failed to fetch featured vehicles'
    };
  }
};

export const getVehicleById = async (id) => {
  try {
    console.log(`API: Fetching vehicle details for ID: ${id}`);
    const response = await api.get(`/vehicles/${id}`);

    console.log('API: Raw vehicle response:', response.data);

    // Process images to ensure proper URLs (like in list endpoints)
    if (response.data && response.data.vehicle && Array.isArray(response.data.vehicle.images)) {
      response.data.vehicle.images = response.data.vehicle.images
        .sort((a, b) => {
          // Sort by is_primary first, then by image_order
          if (a.is_primary && !b.is_primary) return -1;
          if (!a.is_primary && b.is_primary) return 1;
          return (a.image_order || 0) - (b.image_order || 0);
        })
        .map(image => ({
          ...image,
          image_path: image.image_path && image.image_path.startsWith('http')
            ? image.image_path
            : `${API_BASE_URL.replace('/api', '')}/${image.image_path}`
        }));
    }

    // Log hire-specific data for debugging
    if (response.data && response.data.vehicle) {
      const vehicle = response.data.vehicle;
      console.log('API: Vehicle hire data:', {
        listing_type: vehicle.listing_type,
        daily_rate: vehicle.daily_rate,
        weekly_rate: vehicle.weekly_rate,
        monthly_rate: vehicle.monthly_rate,
        make: vehicle.make,
        model: vehicle.model,
        year: vehicle.year
      });
    }

    return response.data;
  } catch (error) {
    console.error(`Error fetching vehicle ${id}:`, error.response?.data || error.message);
    throw error;
  }
};

export const createVehicle = async (vehicleData) => {
  try {
    const formData = new FormData();
    
    // Add vehicle data to form
    Object.keys(vehicleData).forEach(key => {
      if (key !== 'images' && key !== 'videos' && key !== 'documents') {
        formData.append(key, vehicleData[key]);
      }
    });
    
    // Add images if any
    if (vehicleData.images && vehicleData.images.length > 0) {
      vehicleData.images.forEach((image, index) => {
        formData.append('images', {
          uri: image.uri,
          type: 'image/jpeg',
          name: `image_${index}.jpg`
        });
      });
    }
    
    const response = await api.post('/vehicles', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data;
  } catch (error) {
    console.error('Error creating vehicle:', error);
    throw error;
  }
};

// Inquiry endpoints
export const createInquiry = async (inquiryData) => {
  try {
    const response = await api.post('/inquiries', inquiryData);
    return response.data;
  } catch (error) {
    console.error('Error creating inquiry:', error);
    throw error;
  }
};

export const getInquiriesByVehicle = async (vehicleId) => {
  try {
    const response = await api.get(`/inquiries/vehicle/${vehicleId}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching inquiries for vehicle ${vehicleId}:`, error);
    throw error;
  }
};

// Subcategory endpoints
export const getSubcategories = async () => {
  try {
    const response = await api.get('/subcategories');
    return response.data;
  } catch (error) {
    console.error('Error fetching subcategories:', error);
    throw error;
  }
};

export const getSubcategoriesByCategory = async (category) => {
  try {
    const response = await api.get(`/subcategories/category/${category}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching subcategories for ${category}:`, error);
    throw error;
  }
};

// Advanced search endpoint
export const advancedSearchVehicles = async (filters) => {
  try {
    // Map frontend categories to database categories
    const categoryMapping = {
      'trucks': 'trucks',
      'commercial_vehicles': 'commercial_vehicles',
      'buses': 'buses'
    };

    const queryParams = { ...filters };

    // Handle category filtering
    if (queryParams.category) {
      if (queryParams.category === 'others') {
        // For "others", exclude the main categories
        queryParams.exclude_categories = 'trucks,commercial_vehicles,buses';
        console.log('API: Setting exclude_categories for "others" in advancedSearchVehicles:', queryParams.exclude_categories);
        delete queryParams.category; // Remove category param when using exclude
      } else {
        // For specific categories, use the mapped category
        queryParams.category = categoryMapping[queryParams.category] || queryParams.category;
        console.log('API: Setting category filter in advancedSearchVehicles:', queryParams.category);
      }
    }
    if (Array.isArray(queryParams.make)) {
      queryParams.make = queryParams.make.join(',');
    }
    const response = await api.get('/vehicles', { params: queryParams });
    // Process images for each vehicle
    if (response.data && Array.isArray(response.data.vehicles)) {
      response.data.vehicles = response.data.vehicles.map(vehicle => {
        if (vehicle.images && Array.isArray(vehicle.images)) {
          vehicle.images = vehicle.images
            .sort((a, b) => {
              if (a.is_primary && !b.is_primary) return -1;
              if (!a.is_primary && b.is_primary) return 1;
              return (a.image_order || 0) - (b.image_order || 0);
            })
            .map(image => ({
              ...image,
              image_path: image.image_path && image.image_path.startsWith('http')
                ? image.image_path
                : `${API_BASE_URL.replace('/api', '')}/${image.image_path}`
            }));
        }
        return vehicle;
      });
    }
    return response.data;
  } catch (error) {
    console.error('Error performing advanced search:', error.response?.data || error.message);
    throw error;
  }
};

// Get makes by category
export const getMakesByCategory = async (category) => {
  try {
    const response = await api.get(`/vehicles/makes`, {
      params: { category }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching makes:', error.response?.data || error.message);
    throw error;
  }
};

// Get models by make and category
export const getModelsByMake = async (make, category) => {
  try {
    const response = await api.get(`/vehicles/models`, {
      params: { make, category }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching models:', error.response?.data || error.message);
    throw error;
  }
};

// Get categories
export const getCategories = async () => {
  try {
    const response = await api.get('/categories');
    return response.data;
  } catch (error) {
    console.error('Error fetching categories:', error.response?.data || error.message);
    throw error;
  }
};

// Get active categories
export const getActiveCategories = async () => {
  try {
    const response = await api.get('/categories/active');
    return response.data;
  } catch (error) {
    console.error('Error fetching active categories:', error.response?.data || error.message);
    throw error;
  }
};

// User authentication
export const loginUser = async (credentials) => {
  try {
    const response = await api.post('/users/login', credentials);
    return response.data;
  } catch (error) {
    console.error('Error logging in:', error.response?.data || error.message);
    throw error;
  }
};

export const registerUser = async (userData) => {
  try {
    const response = await api.post('/users/register', userData);
    return response.data;
  } catch (error) {
    console.error('Error registering user:', error.response?.data || error.message);
    throw error;
  }
};

// Finance applications
export const createFinanceApplication = async (applicationData) => {
  try {
    const response = await api.post('/finance-applications', applicationData);
    return response.data;
  } catch (error) {
    console.error('Error creating finance application:', error.response?.data || error.message);
    throw error;
  }
};

// Hire bookings
export const createHireBooking = async (bookingData) => {
  try {
    const response = await api.post('/hire-bookings', bookingData);
    return response.data;
  } catch (error) {
    console.error('Error creating hire booking:', error.response?.data || error.message);
    throw error;
  }
};

export const checkVehicleAvailability = async (vehicleId, startDate, endDate) => {
  try {
    const response = await api.get(`/hire-bookings/vehicle/${vehicleId}/availability`, {
      params: { start_date: startDate, end_date: endDate }
    });
    return response.data;
  } catch (error) {
    console.error('Error checking vehicle availability:', error.response?.data || error.message);
    throw error;
  }
};

// Get hire vehicles specifically
export const getHireVehicles = async (limit = 15, category = null, options = {}) => {
  try {
    console.log('API: Fetching hire vehicles with params:', { limit, category });

    // Try multiple approaches to find hire vehicles
    let response;
    let vehicles = [];

    // First try: Look for vehicles with listing_type = 'hire'
    try {
      console.log('API: Trying listing_type = hire...');
      const requestConfig1 = {
        params: {
          listing_type: 'hire',
          limit,
          status: 'available'
        },
        timeout: 10000,
        ...options
      };

      if (category) {
        if (category === 'others') {
          // For "others", exclude the main categories
          requestConfig1.params.exclude_categories = 'trucks,commercial_vehicles,buses';
          console.log('API: Setting exclude_categories for "others" in getHireVehicles:', requestConfig1.params.exclude_categories);
        } else {
          // Map frontend categories to database categories
          const categoryMapping = {
            'trucks': 'trucks',
            'commercial_vehicles': 'commercial_vehicles',
            'buses': 'buses'
          };
          const dbCategory = categoryMapping[category] || category;
          requestConfig1.params.category = dbCategory;
          console.log('API: Setting category filter in getHireVehicles:', dbCategory);
        }
      }

      response = await api.get('/vehicles', requestConfig1);
      vehicles = response.data.vehicles || [];
      console.log('API: Found vehicles with listing_type=hire:', vehicles.length);
    } catch (error) {
      console.log('API: listing_type=hire search failed:', error.message);
    }

    // Second try: Look for vehicles with explicit hire flags only
    if (vehicles.length === 0) {
      try {
        console.log('API: Trying explicit hire flags...');
        const requestConfig2 = {
          params: {
            limit: limit * 3, // Get more to filter strictly
            status: 'available'
          },
          timeout: 10000,
          ...options
        };

        response = await api.get('/vehicles', requestConfig2);
        const allVehicles = response.data.vehicles || [];

        // Filter for hire vehicles on the client side - VERY strict criteria only
        vehicles = allVehicles.filter(vehicle => {
          const isExplicitHire = vehicle.listing_type === 'hire' ||
                                vehicle.for_hire === true ||
                                vehicle.for_hire === 1 ||
                                (vehicle.category && vehicle.category.toLowerCase().includes('hire'));

          if (isExplicitHire) {
            console.log('API: Found explicit hire vehicle:', {
              id: vehicle.vehicle_id,
              listing_type: vehicle.listing_type,
              for_hire: vehicle.for_hire,
              category: vehicle.category
            });
          }

          return isExplicitHire;
        }).slice(0, limit);

        console.log('API: Found vehicles with explicit hire indicators:', vehicles.length);
      } catch (error) {
        console.log('API: explicit hire search failed:', error.message);
      }
    }

    // If no hire vehicles found, don't fallback to all vehicles
    if (vehicles.length === 0) {
      console.log('API: No hire vehicles found in database');
    }

    console.log('API: Final hire vehicles response:', { total: vehicles.length });

    // Process vehicles like in other endpoints
    const processedVehicles = vehicles.map(vehicle => {
      if (vehicle.images && Array.isArray(vehicle.images)) {
        vehicle.images = vehicle.images
          .sort((a, b) => {
            if (a.is_primary && !b.is_primary) return -1;
            if (!a.is_primary && b.is_primary) return 1;
            return (a.image_order || 0) - (b.image_order || 0);
          })
          .map(image => ({
            ...image,
            image_path: image.image_path.startsWith('http')
              ? image.image_path
              : `${API_BASE_URL.replace('/api', '')}/${image.image_path}`
          }));
      }
      return vehicle;
    });

    console.log('API: Processed hire vehicles:', processedVehicles.length);
    if (processedVehicles.length > 0) {
      console.log('API: First hire vehicle sample:', {
        id: processedVehicles[0].vehicle_id,
        make: processedVehicles[0].make,
        model: processedVehicles[0].model,
        listing_type: processedVehicles[0].listing_type,
        daily_rate: processedVehicles[0].daily_rate
      });
    }

    return {
      vehicles: processedVehicles,
      total: processedVehicles.length,
      success: true
    };
  } catch (error) {
    console.error('Error fetching hire vehicles:', error.response?.data || error.message);
    return {
      vehicles: [],
      total: 0,
      error: error.message || 'Failed to fetch hire vehicles'
    };
  }
};