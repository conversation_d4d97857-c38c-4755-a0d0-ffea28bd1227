import React, { useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Animated,
  ScrollView,
} from "react-native";
import {
  Ionicons,
  MaterialCommunityIcons,
} from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { LinearGradient } from 'expo-linear-gradient';

const { width: SCREEN_WIDTH } = Dimensions.get("window");
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 12 : 14;

const AuctionScreen = () => {
  const navigation = useNavigation();

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Subtle pulse animation
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.02,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    );
    pulseAnimation.start();

    return () => pulseAnimation.stop();
  }, []);



  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Simplified Header */}
      <View style={styles.headerContainer}>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Vehicle Auctions</Text>
          <Text style={styles.headerSubtitle}>Live Bidding Platform</Text>
        </View>
      </View>

      {/* Enhanced Coming Soon Banner */}
      <Animated.View
        style={[
          styles.bannerContainer,
          {
            opacity: fadeAnim,
            transform: [
              { scale: scaleAnim },
              { scale: pulseAnim }
            ]
          }
        ]}
      >
        <LinearGradient
          colors={['#8B5CF6', '#A855F7', '#C084FC']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.gradientBackground}
        >
          <View style={styles.bannerContent}>
            {/* Icon Section */}
            <View style={styles.iconContainer}>
              <View style={styles.iconBackground}>
                <MaterialCommunityIcons name="gavel" size={64} color="#FFFFFF" />
              </View>
              <View style={styles.iconAccent}>
                <Ionicons name="hammer" size={24} color="#8B5CF6" />
              </View>
            </View>

            {/* Content Section */}
            <View style={styles.textContainer}>
              <Text style={styles.bannerTitle}>Coming Soon</Text>
              <Text style={styles.bannerSubtitle}>Live Auction Platform</Text>
              <Text style={styles.bannerDescription}>
                Get ready for the most exciting way to buy vehicles! Our live auction platform will feature
                real-time bidding, transparent pricing, and incredible deals on quality vehicles.
              </Text>

              {/* Feature Highlights */}
              <View style={styles.featuresContainer}>
                <View style={styles.featureItem}>
                  <Ionicons name="checkmark-circle" size={20} color="#FFFFFF" />
                  <Text style={styles.featureText}>Live Real-Time Bidding</Text>
                </View>
                <View style={styles.featureItem}>
                  <Ionicons name="checkmark-circle" size={20} color="#FFFFFF" />
                  <Text style={styles.featureText}>Transparent Pricing</Text>
                </View>
                <View style={styles.featureItem}>
                  <Ionicons name="checkmark-circle" size={20} color="#FFFFFF" />
                  <Text style={styles.featureText}>Quality Guaranteed</Text>
                </View>
              </View>

              {/* Call to Action */}
              <View style={styles.ctaContainer}>
                <Text style={styles.ctaText}>Join the auction revolution!</Text>
              </View>
            </View>
          </View>
        </LinearGradient>
      </Animated.View>

    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8fafc",
  },

  // Header Styles
  headerContainer: {
    backgroundColor: "#ffffff",
    paddingHorizontal: 24,
    paddingTop: Platform.OS === "ios" ? 60 : 40,
    paddingBottom: 24,
    borderBottomWidth: 1,
    borderBottomColor: "#e2e8f0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  headerContent: {
    alignItems: "center",
  },
  headerTitle: {
    fontSize: BASE_FONT_SIZE * 2.2,
    fontWeight: "800",
    color: "#1e293b",
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  headerSubtitle: {
    fontSize: BASE_FONT_SIZE * 1.1,
    color: "#64748b",
    fontWeight: "500",
    letterSpacing: 0.2,
  },

  // Enhanced Banner Styles
  bannerContainer: {
    marginHorizontal: 20,
    marginVertical: 40,
    borderRadius: 24,
    overflow: "hidden",
    shadowColor: "#8B5CF6",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  gradientBackground: {
    borderRadius: 24,
  },
  bannerContent: {
    padding: 32,
    alignItems: "center",
  },

  // Icon Styles
  iconContainer: {
    position: "relative",
    marginBottom: 24,
  },
  iconBackground: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 3,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
  iconAccent: {
    position: "absolute",
    bottom: -5,
    right: -5,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#ffffff",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },

  // Text Styles
  textContainer: {
    alignItems: "center",
    width: "100%",
  },
  bannerTitle: {
    fontSize: BASE_FONT_SIZE * 2.4,
    fontWeight: "900",
    color: "#ffffff",
    marginBottom: 8,
    letterSpacing: -0.8,
    textShadowColor: "rgba(0, 0, 0, 0.3)",
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  bannerSubtitle: {
    fontSize: BASE_FONT_SIZE * 1.4,
    fontWeight: "700",
    color: "#ffffff",
    marginBottom: 16,
    letterSpacing: 0.5,
    textTransform: "uppercase",
  },
  bannerDescription: {
    fontSize: BASE_FONT_SIZE * 1.05,
    color: "#ffffff",
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 24,
    paddingHorizontal: 16,
    fontWeight: "400",
    opacity: 0.95,
  },

  // Features Styles
  featuresContainer: {
    width: "100%",
    marginBottom: 24,
  },
  featureItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
    paddingHorizontal: 16,
  },
  featureText: {
    fontSize: BASE_FONT_SIZE * 1.1,
    color: "#ffffff",
    marginLeft: 12,
    fontWeight: "600",
    letterSpacing: 0.3,
  },

  // CTA Styles
  ctaContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
  ctaText: {
    fontSize: BASE_FONT_SIZE * 1.1,
    color: "#ffffff",
    fontWeight: "700",
    textAlign: "center",
    letterSpacing: 0.5,
  },
});

export default AuctionScreen;
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: BASE_FONT_SIZE * 1.5,
    fontWeight: "bold",
    color: "#333",
  },
  headerSubtitle: {
    fontSize: BASE_FONT_SIZE,
    color: "#666",
    marginTop: 2,
  },
  refreshIndicator: {
    fontSize: BASE_FONT_SIZE * 0.8,
    color: "#666",
    marginTop: 2,
  },
  refreshButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: "#f5f5f5",
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: BASE_FONT_SIZE,
    color: "#333",
  },
  infoBanner: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFE6E6",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#FFCCCC",
  },
  infoText: {
    flex: 1,
    marginLeft: 8,
    fontSize: BASE_FONT_SIZE * 0.9,
    color: "#8B0000",
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
  },
  loadingText: {
    marginTop: 12,
    color: "#666",
    fontSize: BASE_FONT_SIZE,
  },
  errorContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
  },
  errorText: {
    fontSize: BASE_FONT_SIZE,
    color: "#FF6B6B",
    textAlign: "center",
    marginTop: 12,
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: "#FF0000",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: "#fff",
    fontSize: BASE_FONT_SIZE,
    fontWeight: "600",
  },
  emptyContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
  },
  emptyTitle: {
    fontSize: BASE_FONT_SIZE * 1.1,
    fontWeight: "bold",
    color: "#333",
    marginTop: 12,
    marginBottom: 4,
  },
  emptySubtitle: {
    fontSize: BASE_FONT_SIZE,
    color: "#666",
    textAlign: "center",
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 20,
  },
  row: {
    justifyContent: "space-between",
    paddingHorizontal: 4,
  },
  cardWrapper: {
    width: "48%",
    marginBottom: 16,
    position: "relative",
  },
  card: {
    width: "100%",
  },
  auctionBadge: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: "#8B0000",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  auctionText: {
    color: "#fff",
    fontSize: BASE_FONT_SIZE * 0.8,
    fontWeight: "bold",
  },
  auctionInfo: {
    backgroundColor: "#f9f9f9",
    padding: 8,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    borderWidth: 1,
    borderTopWidth: 0,
    borderColor: "#e0e0e0",
  },
  bidInfo: {
    marginBottom: 4,
  },
  bidLabel: {
    fontSize: BASE_FONT_SIZE * 0.8,
    color: "#666",
  },
  bidAmount: {
    fontSize: BASE_FONT_SIZE * 0.9,
    fontWeight: "bold",
    color: "#333",
  },
  timeInfo: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  timeLabel: {
    fontSize: BASE_FONT_SIZE * 0.8,
    color: "#666",
  },
  timeRemaining: {
    fontSize: BASE_FONT_SIZE * 0.8,
    fontWeight: "bold",
    color: "#FF0000",
  },
  ended: {
    color: "#999",
  },

  // Coming Soon Banner Styles
  comingSoonBanner: {
    backgroundColor: "#FFF8E1",
    marginHorizontal: 16,
    marginVertical: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#FF8C00",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  comingSoonContent: {
    padding: 24,
    alignItems: "center",
  },
  comingSoonTitle: {
    fontSize: BASE_FONT_SIZE * 1.8,
    fontWeight: "bold",
    color: "#FF8C00",
    marginTop: 12,
    marginBottom: 8,
  },
  comingSoonSubtitle: {
    fontSize: BASE_FONT_SIZE * 1.1,
    fontWeight: "600",
    color: "#E65100",
    textAlign: "center",
    marginBottom: 12,
  },
  comingSoonDescription: {
    fontSize: BASE_FONT_SIZE,
    color: "#BF360C",
    textAlign: "center",
    lineHeight: 20,
    paddingHorizontal: 8,
  },
});

export default AuctionScreen;
