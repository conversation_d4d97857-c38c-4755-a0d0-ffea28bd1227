import React, { useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Animated,
  ScrollView,
  Platform,
} from "react-native";
import {
  Ionicons,
  MaterialCommunityIcons,
} from "@expo/vector-icons";
import { LinearGradient } from 'expo-linear-gradient';

const { width: SCREEN_WIDTH } = Dimensions.get("window");
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 12 : 14;

const Rent2OwnScreen = () => {
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Subtle pulse animation
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.02,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    );
    pulseAnimation.start();

    return () => pulseAnimation.stop();
  }, []);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Simplified Header */}
      <View style={styles.headerContainer}>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Rent to Own</Text>
          <Text style={styles.headerSubtitle}>Flexible Vehicle Financing</Text>
        </View>
      </View>

      {/* Enhanced Coming Soon Banner */}
      <Animated.View
        style={[
          styles.bannerContainer,
          {
            opacity: fadeAnim,
            transform: [
              { scale: scaleAnim },
              { scale: pulseAnim }
            ]
          }
        ]}
      >
        <LinearGradient
          colors={['#FF6B35', '#F7931E', '#FFD23F']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.gradientBackground}
        >
          <View style={styles.bannerContent}>
            {/* Icon Section */}
            <View style={styles.iconContainer}>
              <View style={styles.iconBackground}>
                <MaterialCommunityIcons name="car-key" size={64} color="#FFFFFF" />
              </View>
              <View style={styles.iconAccent}>
                <Ionicons name="construct" size={24} color="#FF6B35" />
              </View>
            </View>

            {/* Content Section */}
            <View style={styles.textContainer}>
              <Text style={styles.bannerTitle}>Coming Soon</Text>
              <Text style={styles.bannerSubtitle}>Rent-to-Own Program</Text>
              <Text style={styles.bannerDescription}>
                We're crafting a revolutionary rent-to-own experience that puts vehicle ownership within your reach.
                Flexible terms, transparent pricing, and a path to ownership designed just for you.
              </Text>

              {/* Feature Highlights */}
              <View style={styles.featuresContainer}>
                <View style={styles.featureItem}>
                  <Ionicons name="checkmark-circle" size={20} color="#FFFFFF" />
                  <Text style={styles.featureText}>Flexible Payment Plans</Text>
                </View>
                <View style={styles.featureItem}>
                  <Ionicons name="checkmark-circle" size={20} color="#FFFFFF" />
                  <Text style={styles.featureText}>No Hidden Fees</Text>
                </View>
                <View style={styles.featureItem}>
                  <Ionicons name="checkmark-circle" size={20} color="#FFFFFF" />
                  <Text style={styles.featureText}>Path to Ownership</Text>
                </View>
              </View>

              {/* Call to Action */}
              <View style={styles.ctaContainer}>
                <Text style={styles.ctaText}>Be the first to know when we launch!</Text>
              </View>
            </View>
          </View>
        </LinearGradient>
      </Animated.View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8fafc",
  },
  // Header Styles
  headerContainer: {
    backgroundColor: "#ffffff",
    paddingHorizontal: 24,
    paddingTop: Platform.OS === "ios" ? 60 : 40,
    paddingBottom: 24,
    borderBottomWidth: 1,
    borderBottomColor: "#e2e8f0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  headerContent: {
    alignItems: "center",
  },
  headerTitle: {
    fontSize: BASE_FONT_SIZE * 2.2,
    fontWeight: "800",
    color: "#1e293b",
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  headerSubtitle: {
    fontSize: BASE_FONT_SIZE * 1.1,
    color: "#64748b",
    fontWeight: "500",
    letterSpacing: 0.2,
  },
  // Enhanced Banner Styles
  bannerContainer: {
    marginHorizontal: 20,
    marginVertical: 40,
    borderRadius: 24,
    overflow: "hidden",
    shadowColor: "#FF6B35",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  gradientBackground: {
    borderRadius: 24,
  },
  bannerContent: {
    padding: 32,
    alignItems: "center",
  },

  // Icon Styles
  iconContainer: {
    position: "relative",
    marginBottom: 24,
  },
  iconBackground: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 3,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
  iconAccent: {
    position: "absolute",
    bottom: -5,
    right: -5,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#ffffff",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },

  // Text Styles
  textContainer: {
    alignItems: "center",
    width: "100%",
  },
  bannerTitle: {
    fontSize: BASE_FONT_SIZE * 2.4,
    fontWeight: "900",
    color: "#ffffff",
    marginBottom: 8,
    letterSpacing: -0.8,
    textShadowColor: "rgba(0, 0, 0, 0.3)",
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  bannerSubtitle: {
    fontSize: BASE_FONT_SIZE * 1.4,
    fontWeight: "700",
    color: "#ffffff",
    marginBottom: 16,
    letterSpacing: 0.5,
    textTransform: "uppercase",
  },
  bannerDescription: {
    fontSize: BASE_FONT_SIZE * 1.05,
    color: "#ffffff",
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 24,
    paddingHorizontal: 16,
    fontWeight: "400",
    opacity: 0.95,
  },

  // Features Styles
  featuresContainer: {
    width: "100%",
    marginBottom: 24,
  },
  featureItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
    paddingHorizontal: 16,
  },
  featureText: {
    fontSize: BASE_FONT_SIZE * 1.1,
    color: "#ffffff",
    marginLeft: 12,
    fontWeight: "600",
    letterSpacing: 0.3,
  },

  // CTA Styles
  ctaContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
  ctaText: {
    fontSize: BASE_FONT_SIZE * 1.1,
    color: "#ffffff",
    fontWeight: "700",
    textAlign: "center",
    letterSpacing: 0.5,
  },
});

export default Rent2OwnScreen;
