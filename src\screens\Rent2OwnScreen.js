import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Dimensions,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from "react-native";
import {
  Ionicons,
  MaterialCommunityIcons,
  FontAwesome5,
  Entypo,
} from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import TruckCard from "../components/TruckCard";
import { getLatestVehicles } from "../utils/api";
import { 
  shuffleArray, 
  setupAutoRefresh, 
  formatRefreshTime, 
  logRefreshActivity,
  REFRESH_INTERVALS 
} from "../utils/listingUtils";

const { width: SCREEN_WIDTH } = Dimensions.get("window");
const BASE_FONT_SIZE = SCREEN_WIDTH < 375 ? 12 : 14;

const Rent2OwnScreen = () => {
  const navigation = useNavigation();
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [lastRefreshTime, setLastRefreshTime] = useState(Date.now());

  const fetchVehicles = async () => {
    try {
      setLoading(true);
      const response = await getLatestVehicles();
      
      // Filter for rent-to-own vehicles and randomize
      const rent2OwnVehicles = (response.vehicles || []).filter(
        vehicle => vehicle.listing_type === 'rent2own' || vehicle.rent_to_own === true
      );
      const shuffledVehicles = shuffleArray(rent2OwnVehicles);
      
      setVehicles(shuffledVehicles);
      setLastRefreshTime(Date.now());
      setError(null);
      
      logRefreshActivity("Rent2OwnScreen", shuffledVehicles.length);
    } catch (err) {
      console.error("Error fetching rent-to-own vehicles:", err);
      setError("Failed to load rent-to-own vehicles. Please try again later.");
      setVehicles([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVehicles();
    
    const refreshInterval = setupAutoRefresh(fetchVehicles, REFRESH_INTERVALS.HOME);
    return () => clearInterval(refreshInterval);
  }, []);

  const filterVehicles = (vehicles) => {
    return vehicles.filter((item) => {
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const searchableText = `${item.make} ${item.model} ${item.year} ${item.description || ""}`.toLowerCase();
        if (!searchableText.includes(query)) return false;
      }
      return true;
    });
  };

  const filteredVehicles = filterVehicles(vehicles);

  const handleTruckPress = (item) => {
    navigation.navigate("TruckDetails", { vehicle: item });
  };

  const renderVehicleItem = ({ item }) => (
    <View style={styles.cardWrapper}>
      <TruckCard
        item={item}
        onPress={() => handleTruckPress(item)}
        width={SCREEN_WIDTH * 0.42} // Reduced from 0.45
        style={styles.card}
      />
      <View style={styles.rent2OwnBadge}>
        <Text style={styles.rent2OwnText}>Rent to Own</Text>
      </View>
    </View>
  );

  return (
    <KeyboardAvoidingView 
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={styles.container}
    >
      {/* Header */}
      <View style={styles.headerContainer}>
        <View style={styles.headerContent}>
          <View style={styles.headerTextContainer}>
            <Text style={styles.headerTitle}>Rent to Own</Text>
            <Text style={styles.headerSubtitle}>
              {loading ? "Loading..." : `${filteredVehicles.length} vehicles available`}
            </Text>
            <Text style={styles.refreshIndicator}>
              Updated {formatRefreshTime(lastRefreshTime)}
            </Text>
          </View>
          <TouchableOpacity 
            style={styles.refreshButton}
            onPress={fetchVehicles}
          >
            <Ionicons name="refresh" size={20} color="#FF0000" />
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View style={styles.searchBar}>
          <Ionicons
            name="search"
            size={20}
            color="#666"
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            placeholder="Search rent-to-own vehicles..."
            placeholderTextColor="#666"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery ? (
            <TouchableOpacity onPress={() => setSearchQuery("")}>
              <Ionicons name="close-circle" size={20} color="#666" />
            </TouchableOpacity>
          ) : null}
        </View>
      </View>

      {/* Info Banner */}
      <View style={styles.infoBanner}>
        <Ionicons name="information-circle" size={20} color="#0066CC" />
        <Text style={styles.infoText}>
          Own your vehicle with flexible payment plans. No large upfront costs required.
        </Text>
      </View>

      {/* Main Content */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF0000" />
          <Text style={styles.loadingText}>Loading rent-to-own vehicles...</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color="#FF6B6B" />
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={fetchVehicles}
          >
            <Text style={styles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      ) : filteredVehicles.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="card-outline" size={48} color="#999" />
          <Text style={styles.emptyTitle}>No rent-to-own vehicles found</Text>
          <Text style={styles.emptySubtitle}>
            Check back later for new rent-to-own opportunities
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredVehicles}
          renderItem={renderVehicleItem}
          keyExtractor={(item) => `rent2own-${item.vehicle_id || item.id}`}
          numColumns={2}
          columnWrapperStyle={styles.row}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
  headerContainer: {
    backgroundColor: "#fff",
    paddingHorizontal: 16,
    paddingTop: Platform.OS === "ios" ? 50 : 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: BASE_FONT_SIZE * 1.5,
    fontWeight: "bold",
    color: "#333",
  },
  headerSubtitle: {
    fontSize: BASE_FONT_SIZE,
    color: "#666",
    marginTop: 2,
  },
  refreshIndicator: {
    fontSize: BASE_FONT_SIZE * 0.8,
    color: "#666",
    marginTop: 2,
  },
  refreshButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: "#f5f5f5",
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: BASE_FONT_SIZE,
    color: "#333",
  },
  infoBanner: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#E6F3FF",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#CCE7FF",
  },
  infoText: {
    flex: 1,
    marginLeft: 8,
    fontSize: BASE_FONT_SIZE * 0.9,
    color: "#0066CC",
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
  },
  loadingText: {
    marginTop: 12,
    color: "#666",
    fontSize: BASE_FONT_SIZE,
  },
  errorContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
  },
  errorText: {
    fontSize: BASE_FONT_SIZE,
    color: "#FF6B6B",
    textAlign: "center",
    marginTop: 12,
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: "#FF0000",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: "#fff",
    fontSize: BASE_FONT_SIZE,
    fontWeight: "600",
  },
  emptyContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
  },
  emptyTitle: {
    fontSize: BASE_FONT_SIZE * 1.1,
    fontWeight: "bold",
    color: "#333",
    marginTop: 12,
    marginBottom: 4,
  },
  emptySubtitle: {
    fontSize: BASE_FONT_SIZE,
    color: "#666",
    textAlign: "center",
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 20,
  },
  row: {
    justifyContent: "space-between",
    paddingHorizontal: 4,
  },
  cardWrapper: {
    width: "48%",
    marginBottom: 16,
    position: "relative",
  },
  card: {
    width: "100%",
  },
  rent2OwnBadge: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: "#0066CC",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  rent2OwnText: {
    color: "#fff",
    fontSize: BASE_FONT_SIZE * 0.8,
    fontWeight: "bold",
  },
});

export default Rent2OwnScreen;
