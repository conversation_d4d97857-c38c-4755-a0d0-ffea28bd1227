// Test script to verify the mock API filter behavior
const axios = require('axios');

const MOCK_API_BASE_URL = 'http://localhost:3001/api';

async function testMockAPI() {
  console.log('Testing Mock API Filters...\n');
  
  try {
    // Test 1: Get all vehicles (baseline)
    console.log('=== TEST 1: Get All Vehicles (Baseline) ===');
    const allVehiclesResponse = await axios.get(`${MOCK_API_BASE_URL}/vehicles`);
    
    const allVehicles = allVehiclesResponse.data.vehicles || [];
    console.log(`Total vehicles returned: ${allVehicles.length}`);
    
    if (allVehicles.length > 0) {
      console.log('All vehicles:');
      allVehicles.forEach((v, i) => {
        console.log(`  ${i+1}. ID: ${v.vehicle_id}, listing_type: ${v.listing_type}, condition_type: ${v.condition_type}, make: ${v.make} ${v.model}`);
      });
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Test 2: Filter by listing_type = 'hire'
    console.log('=== TEST 2: Filter by listing_type = "hire" ===');
    const hireResponse = await axios.get(`${MOCK_API_BASE_URL}/vehicles`, {
      params: { listing_type: 'hire' }
    });
    
    const hireVehicles = hireResponse.data.vehicles || [];
    console.log(`Vehicles with listing_type='hire': ${hireVehicles.length}`);
    
    if (hireVehicles.length > 0) {
      console.log('Hire vehicles:');
      hireVehicles.forEach((v, i) => {
        console.log(`  ${i+1}. ID: ${v.vehicle_id}, listing_type: ${v.listing_type}, make: ${v.make} ${v.model}`);
      });
      
      // Verify all are hire vehicles
      const nonHireVehicles = hireVehicles.filter(v => v.listing_type !== 'hire');
      if (nonHireVehicles.length === 0) {
        console.log('✅ All returned vehicles have listing_type = "hire"');
      } else {
        console.log(`❌ ERROR: Found ${nonHireVehicles.length} vehicles that are NOT hire type`);
      }
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Test 3: Filter by condition_type = 'used'
    console.log('=== TEST 3: Filter by condition_type = "used" ===');
    const usedResponse = await axios.get(`${MOCK_API_BASE_URL}/vehicles`, {
      params: { condition_type: 'used' }
    });
    
    const usedVehicles = usedResponse.data.vehicles || [];
    console.log(`Vehicles with condition_type='used': ${usedVehicles.length}`);
    
    if (usedVehicles.length > 0) {
      console.log('Used vehicles:');
      usedVehicles.forEach((v, i) => {
        console.log(`  ${i+1}. ID: ${v.vehicle_id}, condition_type: ${v.condition_type}, make: ${v.make} ${v.model}`);
      });
      
      // Verify all are used vehicles
      const nonUsedVehicles = usedVehicles.filter(v => v.condition_type !== 'used');
      if (nonUsedVehicles.length === 0) {
        console.log('✅ All returned vehicles have condition_type = "used"');
      } else {
        console.log(`❌ ERROR: Found ${nonUsedVehicles.length} vehicles that are NOT used condition`);
      }
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Test 4: Filter by color = 'White'
    console.log('=== TEST 4: Filter by color = "White" ===');
    const whiteResponse = await axios.get(`${MOCK_API_BASE_URL}/vehicles`, {
      params: { color: 'White' }
    });
    
    const whiteVehicles = whiteResponse.data.vehicles || [];
    console.log(`Vehicles with color='White': ${whiteVehicles.length}`);
    
    if (whiteVehicles.length > 0) {
      console.log('White vehicles:');
      whiteVehicles.forEach((v, i) => {
        console.log(`  ${i+1}. ID: ${v.vehicle_id}, color: ${v.color}, make: ${v.make} ${v.model}`);
      });
      
      // Verify all are white vehicles
      const nonWhiteVehicles = whiteVehicles.filter(v => v.color !== 'White');
      if (nonWhiteVehicles.length === 0) {
        console.log('✅ All returned vehicles have color = "White"');
      } else {
        console.log(`❌ ERROR: Found ${nonWhiteVehicles.length} vehicles that are NOT white`);
      }
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Test 5: Filter by engine_type = 'v8'
    console.log('=== TEST 5: Filter by engine_type = "v8" ===');
    const v8Response = await axios.get(`${MOCK_API_BASE_URL}/vehicles`, {
      params: { engine_type: 'v8' }
    });
    
    const v8Vehicles = v8Response.data.vehicles || [];
    console.log(`Vehicles with engine_type='v8': ${v8Vehicles.length}`);
    
    if (v8Vehicles.length > 0) {
      console.log('V8 vehicles:');
      v8Vehicles.forEach((v, i) => {
        console.log(`  ${i+1}. ID: ${v.vehicle_id}, engine_type: ${v.engine_type}, make: ${v.make} ${v.model}`);
      });
      
      // Verify all are v8 vehicles
      const nonV8Vehicles = v8Vehicles.filter(v => v.engine_type !== 'v8');
      if (nonV8Vehicles.length === 0) {
        console.log('✅ All returned vehicles have engine_type = "v8"');
      } else {
        console.log(`❌ ERROR: Found ${nonV8Vehicles.length} vehicles that are NOT v8`);
      }
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Test 6: Boolean filter - no_accidents = 1
    console.log('=== TEST 6: Filter by no_accidents = 1 ===');
    const noAccidentsResponse = await axios.get(`${MOCK_API_BASE_URL}/vehicles`, {
      params: { no_accidents: 1 }
    });
    
    const noAccidentsVehicles = noAccidentsResponse.data.vehicles || [];
    console.log(`Vehicles with no_accidents=1: ${noAccidentsVehicles.length}`);
    
    if (noAccidentsVehicles.length > 0) {
      console.log('No accidents vehicles:');
      noAccidentsVehicles.forEach((v, i) => {
        console.log(`  ${i+1}. ID: ${v.vehicle_id}, no_accidents: ${v.no_accidents}, make: ${v.make} ${v.model}`);
      });
      
      // Verify all have no accidents
      const accidentVehicles = noAccidentsVehicles.filter(v => v.no_accidents !== 1);
      if (accidentVehicles.length === 0) {
        console.log('✅ All returned vehicles have no_accidents = 1');
      } else {
        console.log(`❌ ERROR: Found ${accidentVehicles.length} vehicles that have accidents`);
      }
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Test 7: Combined filters
    console.log('=== TEST 7: Combined filters (listing_type=sale AND condition_type=used) ===');
    const combinedResponse = await axios.get(`${MOCK_API_BASE_URL}/vehicles`, {
      params: {
        listing_type: 'sale',
        condition_type: 'used'
      }
    });
    
    const combinedVehicles = combinedResponse.data.vehicles || [];
    console.log(`Vehicles with listing_type='sale' AND condition_type='used': ${combinedVehicles.length}`);
    
    if (combinedVehicles.length > 0) {
      console.log('Combined filter results:');
      combinedVehicles.forEach((v, i) => {
        console.log(`  ${i+1}. ID: ${v.vehicle_id}, listing_type: ${v.listing_type}, condition_type: ${v.condition_type}, make: ${v.make} ${v.model}`);
      });
      
      // Validate both conditions
      const invalidVehicles = combinedVehicles.filter(v => v.listing_type !== 'sale' || v.condition_type !== 'used');
      if (invalidVehicles.length === 0) {
        console.log('✅ All returned vehicles match both criteria');
      } else {
        console.log(`❌ ERROR: Found ${invalidVehicles.length} vehicles that don't match both criteria`);
      }
    } else {
      console.log('No vehicles found matching both criteria');
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    console.log('🎉 Mock API filter testing completed!');
    
  } catch (error) {
    console.error('Error testing mock API:', error.response?.data || error.message);
  }
}

// Run the test
testMockAPI();
